#Baber API

## Getting started

+ Add go private by exporting variable to `~/.zshrc` or `~/.bashrc`
```
  export GOPRIVATE=github.com/protrip/*
  export GONOSUMDB=github.com/protrip/*
  export GONOPROXY=github.com/protrip/*
  export GOPROXY=direct
```
+ Config git using ssh: append these lines to `~/.gitconfig` or in the project folder `.git/config`
```
  [url "ssh://**************/"]
        insteadOf = https://github.com/
```

+ Install `Docker`
+ Install `make` tool
  + For Windows: Install make tool from here
  + For MacOS: Install make tool from here
  + For Linux: Install make tool from package manager

## Development Setup
### Steps

1. Clone repository
2. Run `docker compose up -d` for starting the container
3. Run migrate database:
+ Run `make pg-migrate-up` for running the migration up files. 
+ Run `pg-migrate-down` for running the migration down files.



