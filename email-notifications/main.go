package main

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
	"github.com/hajimehoshi/go-mp3"
	"github.com/hajimehoshi/oto/v2"
	"golang.org/x/net/html"
	oauth22 "google.golang.org/api/oauth2/v1"
	"google.golang.org/api/option"
	"log"
	"net/http"
	"os"
	"os/exec"
	"regexp"
	"runtime"
	"strings"
	"time"

	"cloud.google.com/go/pubsub"
	tts "github.com/hegedustibor/htgo-tts"
	handlers "github.com/hegedustibor/htgo-tts/handlers"
	voices "github.com/hegedustibor/htgo-tts/voices"
	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/gmail/v1"
)

type UI struct {
	App         fyne.App
	Container   *fyne.Container
	Window      fyne.Window
	HelloLabel  *widget.Label
	Results     binding.ExternalStringList
	LoginButton *widget.Button
	ResultList  *widget.List
	ResultLabel *widget.Label
}

type Admin struct {
	config       *oauth2.Config
	db           *sql.DB
	userInfo     *oauth22.Userinfoplus
	PreviousInfo *Token
	HistoryId    uint64
	UI           *UI
	Server       *http.Server
}

const (
	ProjectID    = "my-op-427614"
	Topic        = "gmail-notifications"
	Subscription = "gmail-subscription"
)

type Token struct {
	*oauth2.Token
	HistoryId uint64 `json:"history_id" db:"history_id"`
	OldEmail  string
}

type EmailContent struct {
	Subject   string
	From      string
	Body      string
	PlainBody string
	HtmlBody  string
	Sender    string
	Amount    string
}

func main() {
	admin, err := NewAdmin()
	if err != nil {
		log.Fatal(err)
	}

	defer admin.db.Close()
	_, err = admin.DeleteOldRecords()
	if err != nil {
		log.Printf("Failed to delete old records: %v", err)
	}

	// Check for existing token
	token, err := admin.loadToken()
	if err != nil {
		log.Printf("Error loading token: %v", err)
	}
	admin.PreviousInfo = token

	admin.StartServer()
	admin.DrawUI()

	admin.Run()
	select {}
}

func (ad *Admin) DrawUI() {
	ad.UI = &UI{
		App:        app.New(),
		HelloLabel: widget.NewLabel("Xin chào, hãy đăng nhập!"),
		LoginButton: widget.NewButton("Đăng nhập", func() {
			loginURL := "http://localhost:8080/login"
			if err := openBrowser(loginURL); err != nil {
				log.Printf("Could not open browser: %v", err)
				log.Printf("Please open this URL in your browser: %s", loginURL)
			}
		}),
	}
	ad.UI.Window = ad.UI.App.NewWindow("Thông báo nhận tiền từ ngân hàng")

	ad.UI.Results = binding.BindStringList(&[]string{})
	ad.UI.ResultLabel = widget.NewLabel("Kết quả")
	ad.UI.ResultLabel.TextStyle = fyne.TextStyle{Bold: true}
	ad.UI.ResultLabel.Hide()
	ad.UI.ResultList = widget.NewListWithData(ad.UI.Results,
		func() fyne.CanvasObject {
			return widget.NewLabel("template")
		},
		func(i binding.DataItem, o fyne.CanvasObject) {
			o.(*widget.Label).Bind(i.(binding.String))
		})
	ad.UI.Container = container.NewBorder(
		container.NewVBox(ad.UI.HelloLabel, ad.UI.LoginButton),
		nil,
		nil,
		nil,
		container.NewVScroll(ad.UI.ResultList),
	)

	ad.UI.Window.SetContent(ad.UI.Container)
	ad.UI.Window.Resize(fyne.NewSize(400, 400))

}

func (ad *Admin) StartServer() {
	// Setup HTTP server for auth
	ad.Server = &http.Server{Addr: ":8080"}
	http.HandleFunc("/login", func(w http.ResponseWriter, r *http.Request) {
		url := ad.config.AuthCodeURL("state")
		http.Redirect(w, r, url, http.StatusTemporaryRedirect)
	})
	http.HandleFunc("/callback", func(w http.ResponseWriter, r *http.Request) {
		// Process OAuth callback
		if err := ad.handleCallback(w, r); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		// Close server after successful auth
		go func() {
			if err := ad.Server.Shutdown(context.Background()); err != nil {
				log.Printf("Error shutting down server: %v", err)
			}
		}()

		fmt.Fprintf(w, "Thành công! Bạn có thể đóng trang này.")
	})
	// Start server before opening browser
	go func() {
		if err := ad.Server.ListenAndServe(); err != http.ErrServerClosed {
			log.Printf("Server error: %v", err)
		}
	}()
}

func (ad *Admin) Run() {
	ad.UI.Window.ShowAndRun()
}

func initDB() (*sql.DB, error) {
	db, err := sql.Open("sqlite3", "./gmail.db")
	if err != nil {
		return nil, err
	}

	// Create tokens table
	_, err = db.Exec(`
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY,
            access_token TEXT,
            token_type TEXT,
            refresh_token TEXT,
            expiry DATETIME,
            history_id TEXT,
            email TEXT
        )
    `)
	if err != nil {
		return nil, err
	}

	// Create emails table
	_, err = db.Exec(`
        CREATE TABLE IF NOT EXISTS emails (
            id INTEGER PRIMARY KEY,
            message_id TEXT UNIQUE,
            from_address TEXT,
            subject TEXT,
            received_at DATETIME
        )
    `)

	_, err = db.Exec(`
    CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        text TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );`)
	if err != nil {
		return nil, err
	}

	return db, nil
}

func NewAdmin() (*Admin, error) {
	db, err := initDB()
	if err != nil {
		return nil, fmt.Errorf("failed to init db: %v", err)
	}

	config := &oauth2.Config{
		ClientID:     "38204442325-3mndc1nteach15ddop6ern5uejmegv7f.apps.googleusercontent.com",
		ClientSecret: "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",
		RedirectURL:  "http://localhost:8080/callback",
		Scopes: []string{
			"https://www.googleapis.com/auth/userinfo.email",
			"https://www.googleapis.com/auth/userinfo.profile",
			gmail.GmailModifyScope,
			gmail.GmailLabelsScope, // Needed for watch
			"https://www.googleapis.com/auth/pubsub",
		},
		Endpoint: google.Endpoint,
	}

	return &Admin{
		config: config,
		db:     db,
		UI:     &UI{},
	}, nil
}

func (ga *Admin) watchEmails(ctx context.Context, token *oauth2.Token) {
	gmailService, err := gmail.NewService(ctx,
		option.WithTokenSource(ga.config.TokenSource(ctx, token)))
	if err != nil {
		log.Printf("Failed to create Gmail service: %v", err)
		return
	}

	watchRequest := &gmail.WatchRequest{
		LabelIds:  []string{"INBOX"},
		TopicName: "projects/my-op-427614/topics/gmail-notifications", // Replace with your Pub/Sub topic
	}
	log.Println("Started watching emails", " ", ga.HistoryId)

	// Start watching
	_, err = gmailService.Users.Watch("me", watchRequest).Do()
	if err != nil {
		log.Panicf("failed to start watch: %v", err)
	}

	clientOption := option.WithTokenSource(oauth2.StaticTokenSource(token))
	pubsubClient, err := pubsub.NewClient(ctx, ProjectID, clientOption)
	if err != nil {
		log.Fatalf("Failed to create Pub/Sub client: %v", err)
	}

	defer pubsubClient.Close()
	sub := pubsubClient.Subscription(Subscription)
	err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		var notification GmailNotification
		if err := json.Unmarshal(msg.Data, &notification); err != nil {
			log.Printf("Failed to decode message: %v", err)
			msg.Nack()
			return
		}

		log.Printf("Received notification: %+v", notification)
		lastHistoryId := ga.HistoryId
		if ga.HistoryId < 1 {
			lastHistoryId = notification.HistoryID
		}
		ga.saveHistoryId(notification.HistoryID)

		historyList, err := gmailService.Users.History.List("me").
			StartHistoryId(lastHistoryId).
			Do()
		if err != nil {
			log.Printf("Failed to get history: %v", err)
			msg.Nack()
			return
		}

		for _, history := range historyList.History {
			for _, message := range history.Messages {
				// Fetch the full message
				duplicated, err := ga.MessageExist(message.Id)
				if err != nil {
					log.Println("Error when check message exist", err)
					msg.Nack()
					break
				}
				if duplicated {
					continue
				}

				gmailMessage, err := gmailService.Users.Messages.Get("me", message.Id).
					Format("full"). // Request full message format
					Do()
				if err != nil {
					log.Printf("Failed to get message %s: %v", message.Id, err)
					continue
				}

				emailContent := extractEmailContent(gmailMessage)
				if !strings.Contains(emailContent.From, "<EMAIL>") || !strings.Contains(emailContent.Subject, "Thông báo giao dịch thành công") {
					continue
				}
				log.Printf("Processed email %v - %v - Subject: %s, From: %s", notification.HistoryID, message.Id, emailContent.Subject, emailContent.From)
				err = ga.SaveMessageId(message.Id)
				if err != nil {
					log.Printf("Error when save message id %s: %v", message.Id, err)
					msg.Nack()
					break
				}

				err = extractInfo(emailContent)
				if err != nil {
					log.Printf("Failed to extract amount %s: %v", message.Id, err)
					msg.Nack()
					break
				}
				if len(emailContent.Amount) < 1 {
					continue
				}

				ga.UI.Results.Append(emailContent.Sender + ": " + emailContent.Amount + " đồng")
				speak(fmt.Sprintf("Bạn vừa nhận được %s đồng", emailContent.Amount))
			}
		}

		msg.Ack()
	})

}

//func (ga *Admin) saveEmail(email *Email) error {
//	fmt.Println("Received email")
//	fmt.Println("From: ", email.From, "Subject: ", email.Subject)
//	_, err := ga.db.Exec(`
//        INSERT OR REPLACE INTO emails (message_id, from_address, subject, received_at)
//        VALUES (?, ?, ?, ?)
//    `, email.MessageID, email.From, email.Subject, email.ReceivedAt)
//	return err
//}

func (ga *Admin) saveToken(token *oauth2.Token, email string) error {
	// Delete existing token if any
	_, err := ga.db.Exec("DELETE FROM tokens")
	if err != nil {
		return err
	}

	// Insert new token
	_, err = ga.db.Exec(`
        INSERT INTO tokens (access_token, token_type, refresh_token, expiry, email, history_id)
        VALUES (?, ?, ?, ?, ?, ?)
    `, token.AccessToken, token.TokenType, token.RefreshToken, token.Expiry, email, ga.HistoryId)

	return err
}

func (ga *Admin) saveHistoryId(historyId uint64) error {
	if historyId < 1 {
		return nil
	}
	ga.HistoryId = historyId
	// Save historyId
	_, err := ga.db.Exec("UPDATE tokens SET history_id = ?", historyId)
	return err
}

func (ga *Admin) loadToken() (*Token, error) {
	var token oauth2.Token
	var email string
	var historyId uint64
	err := ga.db.QueryRow(`
        SELECT access_token, token_type, refresh_token, expiry, history_id, email
        FROM tokens LIMIT 1
    `).Scan(&token.AccessToken, &token.TokenType, &token.RefreshToken, &token.Expiry, &historyId, &email)

	if err == sql.ErrNoRows {
		return nil, nil
	}
	return &Token{
		Token:     &token,
		HistoryId: historyId,
		OldEmail:  email,
	}, err
}

func openBrowser(url string) error {
	var cmd string
	var args []string

	switch runtime.GOOS {
	case "windows":
		cmd = "cmd"
		args = []string{"/c", "start"}
	case "darwin":
		cmd = "open"
	default: // "linux", "freebsd", "openbsd", "netbsd"
		cmd = "xdg-open"
	}
	args = append(args, url)
	return exec.Command(cmd, args...).Start()
}

func extractEmailContent(msg *gmail.Message) *EmailContent {
	content := &EmailContent{}

	// Extract headers
	for _, header := range msg.Payload.Headers {
		switch header.Name {
		case "Subject":
			content.Subject = header.Value
		case "From":
			content.From = header.Value
		}
	}

	// Extract body
	content.PlainBody, content.HtmlBody = extractMessageBody(msg.Payload)

	// Set the main body field to plain text if available, otherwise HTML
	if content.PlainBody != "" {
		content.Body = content.PlainBody
	} else {
		content.Body = content.HtmlBody
	}

	return content
}

func extractMessageBody(payload *gmail.MessagePart) (plainText, htmlText string) {
	if payload == nil {
		return
	}

	// Handle multipart messages
	if len(payload.Parts) > 0 {
		for _, part := range payload.Parts {
			switch part.MimeType {
			case "text/plain":
				if part.Body != nil && part.Body.Data != "" {
					decoded, err := base64URLDecode(part.Body.Data)
					if err == nil {
						plainText = decoded
					}
				}
			case "text/html":
				if part.Body != nil && part.Body.Data != "" {
					decoded, err := base64URLDecode(part.Body.Data)
					if err == nil {
						htmlText = decoded
					}
				}
			default:
				// Recursively check nested parts
				if len(part.Parts) > 0 {
					p, h := extractMessageBody(part)
					if plainText == "" {
						plainText = p
					}
					if htmlText == "" {
						htmlText = h
					}
				}
			}
		}
	} else if payload.Body != nil && payload.Body.Data != "" {
		// Handle non-multipart messages
		decoded, err := base64URLDecode(payload.Body.Data)
		if err == nil {
			switch payload.MimeType {
			case "text/plain":
				plainText = decoded
			case "text/html":
				htmlText = decoded
			}
		}
	}

	return plainText, htmlText
}

func base64URLDecode(data string) (string, error) {
	// Add padding if needed
	if m := len(data) % 4; m != 0 {
		data += strings.Repeat("=", 4-m)
	}

	decoded, err := base64.URLEncoding.DecodeString(data)
	if err != nil {
		// Try standard base64 if URL encoding fails
		decoded, err = base64.StdEncoding.DecodeString(data)
		if err != nil {
			return "", err
		}
	}

	return string(decoded), nil
}

// Add this to the Admin struct methods
func (ga *Admin) handleCallback(w http.ResponseWriter, r *http.Request) error {
	code := r.URL.Query().Get("code")
	token, err := ga.config.Exchange(r.Context(), code)
	if err != nil {
		return fmt.Errorf("failed to exchange token: %v", err)
	}

	oauth2Service, err := oauth22.NewService(r.Context(),
		option.WithCredentialsJSON([]byte{}),
		option.WithTokenSource(ga.config.TokenSource(r.Context(), token)))
	if err != nil {
		log.Println("Failed to create OAuth2 service: " + err.Error())
		return err
	}
	userInfoService := oauth22.NewUserinfoV2MeService(oauth2Service)
	userInfo, err := userInfoService.Get().Do()
	if err != nil {
		log.Println("Failed to get user info: " + err.Error())
		return err
	}

	ga.userInfo = userInfo
	if ga.userInfo.Email == ga.PreviousInfo.OldEmail {
		ga.HistoryId = ga.PreviousInfo.HistoryId
	}
	if err := ga.saveToken(token, userInfo.Email); err != nil {
		return fmt.Errorf("failed to save token: %v", err)
	}

	ga.UI.HelloLabel.SetText(fmt.Sprintf("Xin chào, %s!", ga.userInfo.Name))
	ga.UI.LoginButton.Hide()
	ga.UI.ResultLabel.Show()
	// Start watching emails
	go ga.watchEmails(context.Background(), token)

	return nil
}

type GmailNotification struct {
	EmailAddress string `json:"emailAddress"`
	HistoryID    uint64 `json:"historyId"`
}

func extractInfo(email *EmailContent) error {
	doc, err := html.Parse(strings.NewReader(email.HtmlBody))
	if err != nil {
		return err
	}

	var traverse func(*html.Node) string
	traverse = func(n *html.Node) string {
		if n.Type == html.TextNode {
			if strings.TrimSpace(n.Data) == "Số tiền" && len(email.Amount) < 1 {
				parent := n.Parent
				if parent != nil {
					sibling := findNextTD(parent.Parent, `(?m)\+([0-9]+\.?[0-9]+)(.*)đ`, "Số tiền")
					if sibling != nil {
						email.Amount = extractTextContent(sibling)
						return email.Amount
					}
				}
			}
			if strings.TrimSpace(n.Data) == "Tên người chuyển" && len(email.Sender) < 1 {
				parent := n.Parent
				if parent != nil {
					sibling := findNextTD(parent.Parent, `(?m)[\w]+`, "Tên người chuyển")
					if sibling != nil {
						email.Sender = strings.TrimSpace(extractTextContent(sibling))
						return email.Sender
					}
				}
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			if r := traverse(c); len(r) > 0 {
				break
			}
		}
		return ""
	}

	traverse(doc)
	if len(email.Amount) > 0 {
		email.Amount = strings.TrimSuffix(strings.TrimPrefix(strings.TrimSpace(email.Amount), "+"), "đ")
	}
	return nil
}

// findNextTD finds the next TD element that contains the amount
func findNextTD(tr *html.Node, reg string, exceptValue string) *html.Node {
	if tr == nil {
		return nil
	}
	re := regexp.MustCompile(reg)

	var targetTD *html.Node
	for td := tr.FirstChild; td != nil; td = td.NextSibling {
		if td.Type == html.ElementNode && td.Data == "td" {
			text := extractTextContent(td.FirstChild)
			if !strings.EqualFold(text, exceptValue) && re.MatchString(text) {
				return td.FirstChild
			}
		}

	}
	return targetTD
}

// extractTextContent extracts all text content from a node
func extractTextContent(n *html.Node) string {
	var buf bytes.Buffer
	var extract func(*html.Node)
	extract = func(n *html.Node) {
		if n.Type == html.TextNode {
			buf.WriteString(n.Data)
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			extract(c)
		}
	}
	extract(n)
	return strings.TrimSpace(buf.String())
}

func speak(text string) {
	speech := tts.Speech{
		Folder:   "audio",
		Language: voices.Vietnamese, // Vietnamese language code
		Handler:  &handlers.Native{},
	}
	playMp3("bell.mp3")
	speech.Speak(text)
	time.Sleep(1 * time.Second)
}

func (ga *Admin) SaveMessageId(text string) error {
	insertSQL := `
    INSERT INTO messages (text, created_at) 
    VALUES (?, ?);
    `

	_, err := ga.db.Exec(insertSQL, text, time.Now())
	if err != nil {
		return err
	}
	return nil
}

func (ga *Admin) MessageExist(text string) (bool, error) {
	var exists bool
	query := `
    SELECT EXISTS(
        SELECT 1 FROM messages 
        WHERE text = ? 
        LIMIT 1
    );
    `

	err := ga.db.QueryRow(query, text).Scan(&exists)
	if err != nil {
		return false, err
	}
	return exists, nil
}

// DeleteOldRecords deletes records older than 30 days and returns the number of deleted records
func (ga *Admin) DeleteOldRecords() (int64, error) {
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)

	deleteSQL := `
    DELETE FROM messages 
    WHERE created_at < ?;
    `

	result, err := ga.db.Exec(deleteSQL, thirtyDaysAgo)
	if err != nil {
		return 0, err
	}

	// Get the number of rows affected
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, err
	}

	return rowsAffected, nil
}

func playMp3(filename string) error {
	// Open the MP3 file
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("error opening file: %v", err)
	}
	defer file.Close()

	// Decode the MP3 file
	decoder, err := mp3.NewDecoder(file)
	if err != nil {
		return fmt.Errorf("error creating decoder: %v", err)
	}

	// Initialize oto context
	context, ready, err := oto.NewContext(decoder.SampleRate(), 2, 2)
	if err != nil {
		return fmt.Errorf("error creating oto context: %v", err)
	}
	<-ready

	// Create a new player
	player := context.NewPlayer(decoder)
	defer player.Close()

	// Start playing
	player.Play()

	// Wait until the audio finishes playing
	for player.IsPlaying() {
		// You might want to add a small sleep here to prevent busy waiting
		// time.Sleep(time.Millisecond * 100)
	}

	return nil
}
