syntax = "proto3";
package exmsg.models;
option go_package = "barber-api/exmsg/services;services";
import "proto/models/admin_user.proto";

message AdminUserRequest {
    int64 id = 1;
    string email = 2;
    repeated string ignore_columns = 3;
    exmsg.models.AdminUser model = 4;
    int64 limit = 5;
    int64 page = 6;
    bool get_model = 7;
    repeated string updated_fields = 8;
    string confirmation_token = 9;
}




