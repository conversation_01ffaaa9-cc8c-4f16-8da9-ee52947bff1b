# Barber API Protobuf Generation

## Prerequisites

- Ensure you have `protoc` version 30.0+ installed. You can download it from [here](https://github.com/protocolbuffers/protobuf/releases).

## Generating Protobuf Files

### Using `protoc` Command

Run the following command to generate the protobuf files:

```
[path_to_protoc] --proto_path=/$GOPATH/src/barber-api --go_out=plugins=grpc:$GOPATH/src [file_path]
```

### Using Shell Function
You can add a function to your .zshrc or .bashrc to simplify the generation process. Add the following function:

```
barber-protogen(){
    [path_to_protoc] --proto_path=/$GOPATH/src/barber-api --go_out=plugins=grpc:$GOPATH/src $1
}
```
After adding the function, reload your shell configuration:
```
source ~/.zshrc  # For zsh users
source ~/.bashrc  # For bash users
```
Now, you can generate the protobuf files by running:
```
barber-protogen [file_path]
```
Replace `[path_to_protoc]` with the actual path to your `protoc` executable and `[file_path]` with the path to your `.proto` file.