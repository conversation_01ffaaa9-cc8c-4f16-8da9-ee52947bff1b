package customer

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/request_parser"
	"barber-api/pkg/utils"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"net/http"
)

type GetCustomersHandler struct {
}

type GetResponse struct {
	Success      bool   `json:"success,omitempty"`
	ErrorMessage string `json:"error_message,omitempty"`
}

func (h *GetCustomersHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dmsRequest := request_parser.OrmParamsParser(r)
	dmsRequest.OrderBy = "id DESC"
	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, GetResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	pagesRes, err := adminDms.GetCustomers(ctx, dmsRequest)
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting sales staff, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
	}

	pages := make([]*models.Customer, 0)
	if pagesRes.List != nil {
		pages = pagesRes.List
	}
	res := &dto.CommonResponseDto{
		Success:   true,
		Customers: pages,
	}

	transhttp.RespondJSON(w, http.StatusOK, res)
}
