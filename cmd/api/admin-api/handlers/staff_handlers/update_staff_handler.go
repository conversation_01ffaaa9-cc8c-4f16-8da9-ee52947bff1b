package staff

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/cache"
	"barber-api/pkg/utils"
	"encoding/json"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type UpdateStaffHandler struct {
}

var ignoreUpdateFields = []string{
	"id",
	"updated_at",
	"created_at",
}

var ignoreGetStaffColumns = []string{
	"name",
	"is_available",
	"created_at",
	"updated_at",
}

type UpdateStaffRequest struct {
	*models.Staff
}

func (h *UpdateStaffHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get staff ID from URL path parameter
	staffId := utils.GetIntFromRequestParams(r, "id")
	if staffId <= 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing id",
		})
		return
	}

	// get staff update columns
	updateFields := utils.GetUpdateFields(r, ignoreUpdateFields)
	if len(updateFields) == 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Nothing to update",
		})
		return
	}

	var req *UpdateStaffRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		uchiha_log.Logger().Warn("Error during parse UpdateStaffRequest: ", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid request body",
		})
		return
	}

	req.Staff.Id = staffId
	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get staff by id
	_, err = adminDms.GetStaff(ctx, &uchiha_models.CommonRequest{
		Id:            staffId,
		IgnoreColumns: ignoreGetStaffColumns,
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting staff, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		transhttp.RespondJSON(w, http.StatusNotFound, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Staff not found",
		})
		return
	}

	// Call update method
	_, err = adminDms.UpdateStaff(ctx, &uchiha_models.CommonRequest{
		Id:            staffId,
		Model:         uchiha_orm.ToStruct(req.Staff),
		UpdateColumns: updateFields,
	})
	if err != nil {
		uchiha_log.Logger().Error(fmt.Sprintf("Error when updating staff, err: %v", err))
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Return success response
	res := &dto.CommonResponseDto{
		Success: true,
	}

	go cache.CleanStaffCache()

	transhttp.RespondJSON(w, http.StatusOK, res)
}
