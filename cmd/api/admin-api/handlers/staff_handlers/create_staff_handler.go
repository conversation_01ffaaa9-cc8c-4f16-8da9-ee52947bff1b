package staff

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/cache"
	"encoding/json"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type CreateStaffHandler struct {
}

type CreateStaffRequest struct {
	*models.Staff
}

func (h *CreateStaffHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var req *CreateStaffRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		uchiha_log.Logger().Warn("Error during parse CreateStaffRequest: ", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid request body",
		})
		return
	}
	if len(req.Name) < 1 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing name",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Call create method
	_, err = adminDms.CreateStaff(ctx, &uchiha_models.CommonRequest{
		Model: uchiha_orm.ToStruct(req.Staff),
	})
	if err != nil {
		uchiha_log.Logger().Error(fmt.Sprintf("Error when creating staff, err: %v", err))
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Return success response with created ID
	res := &dto.CommonResponseDto{
		Success: true,
	}

	go cache.CleanStaffCache()

	transhttp.RespondJSON(w, http.StatusOK, res)
}
