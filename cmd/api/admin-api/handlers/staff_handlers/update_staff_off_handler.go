package staff

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"barber-api/pkg/cache"
	"barber-api/pkg/utils"
	"encoding/json"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type UpdateStaffOffHandler struct {
}

type UpdateStaffOffRequest struct {
	StaffOffs []*models.StaffOff `json:"staff_offs"`
	Add       bool               `json:"add"`
}

func (h *UpdateStaffOffHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	staffId := utils.GetIntFromRequestParams(r, "id")
	if staffId < 1 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing id",
		})
		return
	}

	var req UpdateStaffOffRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		uchiha_log.Logger().Warn("Error during parse CreateServiceRequest: ", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid request body",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Create maps for easier lookup
	upsertMap := make(map[string]*models.StaffOff)
	updateList := make([]*models.StaffOff, 0)

	deleteIds := make([]int64, 0)
	for _, sc := range req.StaffOffs {
		sc.StaffId = staffId
		sc.Id = 0
		upsertMap[sc.TimePeriodName] = sc
		updateList = append(updateList, sc)
	}

	if !req.Add {
		existing, err := adminDms.GetStaffOffs(ctx, &uchiha_models.CommonRequest{
			Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
				{
					Field: "staff_id",
					Mode:  uchiha_orm.Eq,
					Value: staffId,
				},
			}),
		})
		if err != nil && !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error("Error getting existing staffOff", "err", err.Error())
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		if existing != nil {
			for _, sc := range existing.List {
				if _, ok := upsertMap[sc.TimePeriodName]; !ok {
					deleteIds = append(deleteIds, sc.Id)
				}
			}
		}
	}

	_, err = adminDms.UpdateStaffOffInStaff(ctx, &services.UpdateStaffOff{
		Upsert: updateList,
		Delete: deleteIds,
	})
	if err != nil {
		uchiha_log.Logger().Error("Error updating service customs", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	go cache.CleanStaffOffCache()

	transhttp.RespondJSON(w, http.StatusOK, dto.CommonResponseDto{
		Success: true,
	})
}
