package staff

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/utils"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
	"time"
)

type GetStaffByIdHandler struct {
}

func (h *GetStaffByIdHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get staff ID from URL path parameter
	id := utils.GetIntFromRequestParams(r, "id")
	if id <= 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Invalid staff id",
		})
		return
	}

	loc, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err != nil {
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "failed to load timezone",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get Staff
	staff, err := adminDms.GetStaff(ctx, &uchiha_models.CommonRequest{
		Id: id,
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting staff by ID, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		transhttp.RespondJSON(w, http.StatusNotFound, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Staff not found",
		})
		return
	}

	// Get Staff off
	staffOffs, err := adminDms.GetStaffOffs(ctx, &uchiha_models.CommonRequest{
		CustomizedSelect: "id, staff_id, TO_CHAR(date :: DATE, 'yyyy-mm-dd') as date, time_period_name, created_at, updated_at",
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "staff_id",
				Mode:  uchiha_orm.Eq,
				Value: id,
			},
			{
				Field: "date",
				Mode:  uchiha_orm.Gte,
				Value: time.Now().In(loc),
			},
		}),
		Limit:   10000,
		OrderBy: "date DESC, time_period_name asc",
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting staff off, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	if staffOffs == nil {
		staffOffs = &models.StaffOffs{}
	}

	transhttp.RespondJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"staff": &dto.AdminStaffDto{
			StaffDto: &dto.StaffDto{
				Id:   staff.Id,
				Name: staff.Name,
			},
			IsAvailable: false,
			StaffOffs:   staffOffs.List,
		},
	})
}
