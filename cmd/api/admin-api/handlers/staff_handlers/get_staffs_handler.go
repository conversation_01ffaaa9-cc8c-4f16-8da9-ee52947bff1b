package staff

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/utils"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type GetStaffHandler struct {
}

func (h *GetStaffHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dmsRequest := generateGetStaffsRequest(r)

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.<PERSON>rror())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	pagesRes, err := adminDms.GetStaffs(ctx, dmsRequest)
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting staff, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
	}

	pages := make([]*models.Staff, 0)
	if pagesRes.List != nil {
		pages = pagesRes.List
	}
	res := &dto.CommonResponseDto{
		Success: true,
		Staffs:  pages,
	}

	transhttp.RespondJSON(w, http.StatusOK, res)
}

func generateGetStaffsRequest(r *http.Request) *uchiha_models.CommonRequest {
	return &uchiha_models.CommonRequest{
		Limit:   5000,
		OrderBy: "updated_at DESC",
	}
}
