package service

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/pkg/cache"
	"barber-api/pkg/utils"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type DeleteServiceHandler struct {
}

func (h *DeleteServiceHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	serviceId := utils.GetIntFromRequestParams(r, "id")
	if serviceId < 1 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing id",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	_, err = adminDms.DeleteService(ctx, &uchiha_models.CommonRequest{
		Id: serviceId,
	})
	if err != nil {
		uchiha_log.Logger().Error(fmt.Sprintf("Error when deleting staff, err: %v", err))
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	go cache.CleanServiceCache()

	transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
		Success: true,
	})
}
