package service

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/pkg/utils"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type GetServicesHandler struct {
}

func (h *GetServicesHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	res, err := adminDms.GetServices(ctx, &uchiha_models.CommonRequest{
		OrderBy: "updated_at DESC",
		Limit:   5000,
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting staff, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
	}

	transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
		Success:  true,
		Services: res.List,
	})
}
