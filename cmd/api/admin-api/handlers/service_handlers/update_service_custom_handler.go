package service

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"barber-api/pkg/cache"
	"barber-api/pkg/utils"
	"encoding/json"
	"errors"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type UpdateServiceCustomHandler struct {
}

type UpdateServiceCustomRequest struct {
	ServiceCustoms []*models.ServiceCustom `json:"service_customs"`
}

func (h *UpdateServiceCustomHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get service ID from URL path parameter
	serviceId := utils.GetIntFromRequestParams(r, "id")
	if serviceId < 1 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing id",
		})
		return
	}

	staffId := utils.GetIntFromRequestParams(r, "staff_id")

	var req UpdateServiceCustomRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		uchiha_log.Logger().Warn("Error during parse CreateServiceRequest: ", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid request body",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get existing service customs for this service and staff
	existingServiceCustoms, err := adminDms.GetServiceCustoms(ctx, &uchiha_models.CommonRequest{
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "service_id",
				Mode:  uchiha_orm.Eq,
				Value: serviceId,
			},
			{
				Field: "staff_id",
				Mode:  uchiha_orm.Eq,
				Value: staffId,
			},
		}),
	})
	if err != nil && !utils.IsNotFoundError(err) {
		uchiha_log.Logger().Error("Error getting existing service customs", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Create maps for easier lookup
	upsertMap := make(map[string]*models.ServiceCustom)
	updateList := make([]*models.ServiceCustom, 0)

	deleteIds := make([]int64, 0)
	for _, sc := range req.ServiceCustoms {
		sc.ServiceId = serviceId
		sc.StaffId = staffId
		sc.Id = 0
		upsertMap[sc.TimePeriodName] = sc
		updateList = append(updateList, sc)
	}

	// Key format: "time_period_name"
	if existingServiceCustoms != nil {
		for _, sc := range existingServiceCustoms.List {
			if _, ok := upsertMap[sc.TimePeriodName]; !ok {
				deleteIds = append(deleteIds, sc.Id)
			}
		}
	}

	_, err = adminDms.UpdateServiceCustomInService(ctx, &services.UpdateServiceCustom{
		Upsert: updateList,
		Delete: deleteIds,
	})
	if err != nil {
		uchiha_log.Logger().Error("Error updating service customs", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	go cache.CleanServiceCustom()

	transhttp.RespondJSON(w, http.StatusOK, dto.CommonResponseDto{
		Success: true,
	})
}

func validateUpdateServiceCustomRequest(req *UpdateServiceCustomRequest, serviceId, staffId int64) error {
	if req == nil || len(req.ServiceCustoms) == 0 {
		return errors.New("service_customs is required")
	}

	// Check for duplicate time periods
	timePeriods := make(map[string]bool)
	for _, sc := range req.ServiceCustoms {
		if sc.TimePeriodName == "" {
			return errors.New("time_period_name is required for all service customs")
		}

		if _, exists := timePeriods[sc.TimePeriodName]; exists {
			return fmt.Errorf("duplicate time period: %s", sc.TimePeriodName)
		}
		timePeriods[sc.TimePeriodName] = true

		if sc.Price < 0 {
			return errors.New("price cannot be negative")
		}
	}

	return nil
}
