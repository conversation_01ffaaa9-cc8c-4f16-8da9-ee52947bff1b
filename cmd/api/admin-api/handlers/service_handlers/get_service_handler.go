package service

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/utils"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type GetServiceHandler struct {
}

func (h *GetServiceHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get staff ID from URL path parameter
	id := utils.GetIntFromRequestParams(r, "id")
	if id <= 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Invalid staff id",
		})
		return
	}
	service, err := adminDms.GetService(ctx, &uchiha_models.CommonRequest{
		Id: id,
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting staff, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		transhttp.RespondJSON(w, http.StatusNotFound, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Staff not found",
		})
		return
	}
	res, err := adminDms.GetServiceCustoms(ctx, &uchiha_models.CommonRequest{
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "service_id",
				Mode:  uchiha_orm.Eq,
				Value: id,
			},
		}),
		Limit: 10000,
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting staff, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
	}
	if res == nil {
		res = &models.ServiceCustoms{}
	}
	response := &dto.AdminServiceDto{
		ServiceDto:     dto.ConvertToServiceDto(service),
		ServiceCustoms: res.List,
	}

	transhttp.RespondJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"service": response,
	})
}
