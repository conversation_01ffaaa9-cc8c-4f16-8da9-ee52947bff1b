package service

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/cache"
	"barber-api/pkg/utils"
	"encoding/json"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type UpdateServiceHandler struct {
}

var ignoreUpdateFields = []string{
	"id",
	"updated_at",
	"created_at",
}

var ignoreGetServiceColumns = []string{
	"created_at",
	"updated_at",
}

type UpdateServiceRequest struct {
	*models.Service
}

func (h *UpdateServiceHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get service ID from URL path parameter
	serviceId := utils.GetIntFromRequestParams(r, "id")
	if serviceId <= 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing id",
		})
		return
	}

	// get service update columns
	updateFields := utils.GetUpdateFields(r, ignoreUpdateFields)
	if len(updateFields) == 0 {
		transhttp.RespondJSON(w, http.StatusOK, dto.CommonResponseDto{
			Success: true,
		})
		return
	}

	var req UpdateServiceRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		uchiha_log.Logger().Warn("Error during parse CreateServiceRequest: ", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid request body",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get service by id
	_, err = adminDms.GetService(ctx, &uchiha_models.CommonRequest{
		Id:            serviceId,
		IgnoreColumns: ignoreGetServiceColumns,
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting service, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		transhttp.RespondJSON(w, http.StatusNotFound, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Service not found",
		})
		return
	}

	// Call update method
	_, err = adminDms.UpdateService(ctx, &uchiha_models.CommonRequest{
		Id:            serviceId,
		Model:         uchiha_orm.ToStruct(req.Service),
		UpdateColumns: updateFields,
	})
	if err != nil {
		uchiha_log.Logger().Error(fmt.Sprintf("Error when updating service, err: %v", err))
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Return success response
	res := &dto.CommonResponseDto{
		Success: true,
	}

	go cache.CleanServiceCache()

	transhttp.RespondJSON(w, http.StatusOK, res)
}
