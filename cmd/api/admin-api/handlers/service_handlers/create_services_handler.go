package service

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/cache"
	"encoding/json"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type CreateServicesHandler struct{}

type CreateServiceBody struct {
	*models.Service
}

func (h *CreateServicesHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	var req CreateServiceBody
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		uchiha_log.Logger().Warn("Error during parse CreateServiceRequest: ", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid request body",
		})
		return
	}
	if len(req.Name) < 1 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing name",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	_, err = adminDms.CreateServices(ctx, &uchiha_models.CommonRequest{
		List: uchiha_orm.ToSliceStruct([]*models.Service{req.Service}),
	})
	if err != nil {
		uchiha_log.Logger().Error("Create services failed", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	res := &dto.CommonResponseDto{
		Success: true,
	}

	go cache.CleanServiceCache()

	transhttp.RespondJSON(w, http.StatusOK, res)
}
