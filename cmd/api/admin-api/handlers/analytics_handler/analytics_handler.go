package analytics_handler

import (
	"barber-api/cmd/dms/server"
	"context"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
)

type AnalyticsHandler struct {
}

type AnalyticsRequest struct {
	StylistIds []int64 `json:"stylist_ids"`
	From       string  `json:"from"`
	To         string  `json:"to"`
	Mode       string  `json:"mode"`
}

type AnalyticsResponse struct {
	Success      bool                          `json:"success"`
	Data         map[string]map[string]float64 `json:"data"`
	ErrorMessage string                        `json:"error_message,omitempty"`
}

func (h *AnalyticsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse query parameters
	req, err := parseAnalyticsRequest(r)
	if err != nil {
		transhttp.RespondJSON(w, http.StatusBadRequest, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Validate request
	if err := validateAnalyticsRequest(req); err != nil {
		transhttp.RespondJSON(w, http.StatusBadRequest, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get analytics data
	data, err := getAnalyticsData(ctx, adminDms, req)
	if err != nil {
		uchiha_log.Logger().Error("Error getting analytics data", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, AnalyticsResponse{
		Success: true,
		Data:    data,
	})
}

func parseAnalyticsRequest(r *http.Request) (*AnalyticsRequest, error) {
	req := &AnalyticsRequest{}

	// Parse stylist_ids
	stylistIdsStr := r.URL.Query().Get("stylist_ids")
	if stylistIdsStr != "" {
		stylistIdStrs := strings.Split(stylistIdsStr, ",")
		for _, idStr := range stylistIdStrs {
			id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
			if err != nil {
				return nil, fmt.Errorf("invalid stylist_id: %s", idStr)
			}
			req.StylistIds = append(req.StylistIds, id)
		}
	}

	// Parse from and to dates
	req.From = r.URL.Query().Get("from")
	req.To = r.URL.Query().Get("to")
	req.Mode = r.URL.Query().Get("mode")

	return req, nil
}

func validateAnalyticsRequest(req *AnalyticsRequest) error {
	if req.From == "" {
		return errors.New("from date is required")
	}
	if req.To == "" {
		return errors.New("to date is required")
	}

	// Validate date format (YYYY/MM/DD)
	_, err := time.Parse("2006/01/02", req.From)
	if err != nil {
		return errors.New("invalid from date format. Expected YYYY/MM/DD")
	}

	_, err = time.Parse("2006/01/02", req.To)
	if err != nil {
		return errors.New("invalid to date format. Expected YYYY/MM/DD")
	}

	// Validate mode
	if req.Mode != "day" && req.Mode != "month" && req.Mode != "year" {
		return errors.New("mode must be one of: day, month, year")
	}

	return nil
}

func getAnalyticsData(ctx context.Context, adminDms *server.AdminDmsServer, req *AnalyticsRequest) (map[string]map[string]float64, error) {
	// Build the SQL query
	query, args := buildAnalyticsQuery(req)

	// Execute the query
	rows, err := adminDms.ExecuteRawQuery(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	result := make(map[string]map[string]float64)

	// Process the results
	for rows.Next() {
		var staffName string
		var period string
		var total float64

		if err := rows.Scan(&staffName, &period, &total); err != nil {
			return nil, err
		}

		if result[staffName] == nil {
			result[staffName] = make(map[string]float64)
		}
		result[staffName][period] = total
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return result, nil
}

func buildAnalyticsQuery(req *AnalyticsRequest) (string, []interface{}) {
	var selectClause, whereClause, groupByClause string
	var args []interface{}
	argIndex := 1

	// Convert dates from YYYY/MM/DD to YYYY-MM-DD for SQL
	fromDate := strings.ReplaceAll(req.From, "/", "-")
	toDate := strings.ReplaceAll(req.To, "/", "-")

	// Build SELECT clause based on mode and stylist filtering
	if len(req.StylistIds) == 0 {
		// No stylist filtering - group all as "All"
		selectClause = "'All' as staff_name"
	} else {
		// With stylist filtering - use staff names
		selectClause = "COALESCE(s.name, 'Unknown') as staff_name"
	}

	// Add period formatting based on mode
	switch req.Mode {
	case "day":
		selectClause += ", DATE(t.created_at) as period"
	case "month":
		selectClause += ", DATE_FORMAT(t.created_at, '%Y-%m') as period"
	case "year":
		selectClause += ", YEAR(t.created_at) as period"
	}

	selectClause += ", SUM(t.price) as total"

	// Build FROM clause
	fromClause := "FROM transactions t"
	if len(req.StylistIds) > 0 {
		fromClause += " LEFT JOIN staffs s ON t.staff_id = s.id"
	}

	// Build WHERE clause
	whereConditions := []string{
		fmt.Sprintf("DATE(t.created_at) >= $%d", argIndex),
		fmt.Sprintf("DATE(t.created_at) <= $%d", argIndex+1),
	}
	args = append(args, fromDate, toDate)
	argIndex += 2

	if len(req.StylistIds) > 0 {
		placeholders := make([]string, len(req.StylistIds))
		for i, id := range req.StylistIds {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, id)
			argIndex++
		}
		whereConditions = append(whereConditions, fmt.Sprintf("t.staff_id IN (%s)", strings.Join(placeholders, ",")))
	}

	whereClause = "WHERE " + strings.Join(whereConditions, " AND ")

	// Build GROUP BY clause
	if len(req.StylistIds) == 0 {
		groupByClause = "GROUP BY period"
	} else {
		groupByClause = "GROUP BY staff_name, period"
	}

	// Combine all parts
	query := fmt.Sprintf("SELECT %s %s %s %s ORDER BY staff_name, period",
		selectClause, fromClause, whereClause, groupByClause)

	return query, args
}
