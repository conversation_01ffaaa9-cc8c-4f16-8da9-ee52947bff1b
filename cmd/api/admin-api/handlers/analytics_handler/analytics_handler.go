package analytics_handler

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
)

type AnalyticsHandler struct {
}

type AnalyticsRequest struct {
	StylistIds []int64 `json:"stylist_ids"`
	From       string  `json:"from"`
	To         string  `json:"to"`
	Mode       string  `json:"mode"`
}

type AnalyticsResponse struct {
	Success bool                       `json:"success"`
	Data    map[string]map[string]float64 `json:"data"`
	ErrorMessage string                `json:"error_message,omitempty"`
}

func (h *AnalyticsHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse query parameters
	req, err := parseAnalyticsRequest(r)
	if err != nil {
		transhttp.RespondJSON(w, http.StatusBadRequest, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Validate request
	if err := validateAnalyticsRequest(req); err != nil {
		transhttp.RespondJSON(w, http.StatusBadRequest, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get analytics data
	data, err := getAnalyticsData(ctx, adminDms, req)
	if err != nil {
		uchiha_log.Logger().Error("Error getting analytics data", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, AnalyticsResponse{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, AnalyticsResponse{
		Success: true,
		Data:    data,
	})
}

func parseAnalyticsRequest(r *http.Request) (*AnalyticsRequest, error) {
	req := &AnalyticsRequest{}

	// Parse stylist_ids
	stylistIdsStr := r.URL.Query().Get("stylist_ids")
	if stylistIdsStr != "" {
		stylistIdStrs := strings.Split(stylistIdsStr, ",")
		for _, idStr := range stylistIdStrs {
			id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
			if err != nil {
				return nil, fmt.Errorf("invalid stylist_id: %s", idStr)
			}
			req.StylistIds = append(req.StylistIds, id)
		}
	}

	// Parse from and to dates
	req.From = r.URL.Query().Get("from")
	req.To = r.URL.Query().Get("to")
	req.Mode = r.URL.Query().Get("mode")

	return req, nil
}

func validateAnalyticsRequest(req *AnalyticsRequest) error {
	if req.From == "" {
		return errors.New("from date is required")
	}
	if req.To == "" {
		return errors.New("to date is required")
	}

	// Validate date format (YYYY/MM/DD)
	_, err := time.Parse("2006/01/02", req.From)
	if err != nil {
		return errors.New("invalid from date format. Expected YYYY/MM/DD")
	}

	_, err = time.Parse("2006/01/02", req.To)
	if err != nil {
		return errors.New("invalid to date format. Expected YYYY/MM/DD")
	}

	// Validate mode
	if req.Mode != "day" && req.Mode != "month" && req.Mode != "year" {
		return errors.New("mode must be one of: day, month, year")
	}

	return nil
}
