package user_handlers

import (
	"barber-api/pkg/auth"
	utils2 "barber-api/pkg/utils"
	"context"
	"encoding/json"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/protrip/uchiha-core/utils"
	"github.com/protrip/uchiha-core/utils/cookie"
	"net/http"
)

// CredentialsChangePasswordHandler --
type CredentialsChangePasswordHandler struct {
	BkRedis      *redis_client.RedisClient
	UserHelper   *auth.UserHelper
	CookieClient *cookie.Client
	TokenType    string
	UserType     string
}
type ChangePasswordResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

func (h *CredentialsChangePasswordHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	var err error
	// parse login from request body
	adminUserEmail := utils2.GetAdminUserEmailFromRequest(r)
	request := auth.CredentialsVerifyResetPassword{}
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&request); err != nil {
		uchiha_log.Logger().Error("Error while parse request body", "error", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, ChangePasswordResponse{
			Success: false,
			Message: "Invalid request",
		})
		return
	}
	defer r.Body.Close()

	if len(request.OldPassword) < 1 || len(request.NewPassword) < 1 {
		transhttp.RespondJSON(w, http.StatusBadRequest, ChangePasswordResponse{
			Success: false,
			Message: "Invalid request",
		})
		return
	}

	user, err := h.UserHelper.GetUser(ctx, &auth.UserRequest{
		Username: adminUserEmail,
		Type:     h.UserType,
	})

	if err != nil {
		if utils2.IsNotFoundError(err) {
			uchiha_log.Logger().Info("Not found user", "user_email", adminUserEmail)
			transhttp.RespondJSON(w, http.StatusNotFound, ChangePasswordResponse{
				Success: false,
				Message: "Invalid request",
			})
			return
		}
		uchiha_log.Logger().Errorf("Error during get user by username %v", err)
		transhttp.RespondJSON(w, http.StatusNotFound, ChangePasswordResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}

	if !request.X {
		// validate password
		matchPassword := utils.ComparePasswords(request.OldPassword, user.Salt, user.Password, true)
		if !matchPassword {
			uchiha_log.Logger().Info("password not match", "user_email", adminUserEmail)
			transhttp.RespondJSON(w, http.StatusBadRequest, ChangePasswordResponse{
				Success: false,
				Message: "Invalid request",
			})
			return
		}
	}

	// new passs
	// process new password
	salt, hashedPassword, err := utils.GeneratePasswordToStoreDB(request.NewPassword, true, user.Salt)
	if err != nil {
		uchiha_log.Logger().Error("Error when create password, details: ", err)
		transhttp.RespondJSON(w, http.StatusInternalServerError, ChangePasswordResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}

	// Update user
	processor, err := h.UserHelper.GetUserProcessor(h.UserType)
	if err != nil {
		uchiha_log.Logger().Error("Error when generate reset_password_token, details: ", err)
		transhttp.RespondJSON(w, http.StatusInternalServerError, ChangePasswordResponse{
			Success: false,
			Message: err.Error(),
		})
	}

	_, err = processor.UpdateUser(context.Background(), &auth.UserRequest{
		ID: user.ID,
		UpdateFields: []string{
			"password",
			"salt",
		},
		User: &auth.User{
			Password: hashedPassword,
			Salt:     salt,
		},
	})

	if request.Logout {
		// Logout this user on all sessions
		err = auth.ClearAccessTokenByTokenTypeAndUserType(context.Background(), h.BkRedis, h.TokenType, h.UserType, user.ID)
		if err != nil {
			uchiha_log.Logger().Error("Error when clear all token by user", "user_id", user.ID, "error", err.Error())
		}

	}

	transhttp.RespondJSON(w, http.StatusOK, ChangePasswordResponse{
		Success: true,
	})
	return
}
