package user_handlers

import (
	"barber-api/pkg/auth"
	auth_processor "barber-api/pkg/auth"
	"barber-api/pkg/utils"
	"encoding/json"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	"github.com/protrip/uchiha-core/core/tokenmanager"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	utils2 "github.com/protrip/uchiha-core/utils"
	"github.com/protrip/uchiha-core/utils/cookie"
	"net/http"
)

// CredentialsAuthHandler --
type CredentialsAuthHandler struct {
	Redis        *redis_client.RedisClient
	UserHelper   *auth_processor.UserHelper
	CookieClient *cookie.Client
	TokenType    string
	UserType     string
}

type authResponse struct {
	Success      bool     `json:"success"`
	ErrorCode    string   `json:"errorCode,omitempty"`
	ErrorMessage string   `json:"errorMessage,omitempty"`
	Data         AuthData `json:"data"`
}

type AuthData struct {
	Avatar       string   `json:"avatar,omitempty"`
	Username     string   `json:"username,omitempty"`
	Nickname     string   `json:"nickname,omitempty"`
	Roles        []string `json:"roles,omitempty"`
	Permissions  []string `json:"permissions,omitempty"`
	AccessToken  string   `json:"accessToken,omitempty"`
	RefreshToken string   `json:"refreshToken,omitempty"`
	Expires      string   `json:"expires,omitempty"`
}

func (h *CredentialsAuthHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	var err error
	// parse login from request body
	login := auth.Credentials{}
	decoder := json.NewDecoder(r.Body)
	if err := decoder.Decode(&login); err != nil {
		uchiha_log.Logger().Error("Error while parse request body", "error", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, authResponse{
			Success:   false,
			ErrorCode: "invalid_request",
		})
		return
	}
	defer r.Body.Close()

	if len(login.Username) == 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, authResponse{
			Success:   false,
			ErrorCode: "username_required",
		})
		return
	}

	user, err := h.UserHelper.GetUser(ctx, &auth.UserRequest{
		Username: login.Username,
		Type:     h.UserType,
	})

	if err != nil {
		if utils.IsNotFoundError(err) {
			transhttp.RespondJSON(w, http.StatusBadRequest, authResponse{
				Success:   false,
				ErrorCode: "invalid_request",
			})
			return
		}
		uchiha_log.Logger().Errorf("Error during get user by username %v", err)
		transhttp.RespondJSON(w, http.StatusBadRequest, authResponse{
			Success:      false,
			ErrorCode:    "internal_server_error",
			ErrorMessage: err.Error(),
		})
		return
	}

	// validate password
	matchPassword := utils2.ComparePasswords(login.Password, user.Salt, user.Password, true)
	if !matchPassword {
		transhttp.RespondJSON(w, http.StatusBadRequest, authResponse{
			Success:   false,
			ErrorCode: "password_not_match",
		})
		return
	}

	// generate token
	req := tokenmanager.AccessTokenRequest{
		TokenType: h.TokenType,
		UserID:    user.ID,
		Username:  user.Email,
		Email:     user.Email,
		IsPrivate: false,
		Scopes:    nil,
		Roles:     []string{h.UserType},
	}

	utp := &tokenmanager.TokenProcessor{
		TokenManager:   tokenmanager.NewTokenManager(h.Redis, auth_processor.TokenDefinitions),
		ResponseWriter: w,
		Request:        r,
		TokenType:      h.TokenType,
		CookieClient:   h.CookieClient,
	}

	token, err := utp.Process(ctx, &req)
	if err != nil {
		transhttp.RespondError(w, http.StatusInternalServerError, err.Error())
		return
	}

	transhttp.RespondJSON(w, http.StatusOK, authResponse{
		Data: AuthData{
			Avatar:       "",
			Username:     "admin",
			Nickname:     "Admin",
			Roles:        []string{"admin"},
			Permissions:  []string{"*:*:*"},
			AccessToken:  token,
			RefreshToken: "",
			Expires:      "",
		},
		Success: true,
	})
}
