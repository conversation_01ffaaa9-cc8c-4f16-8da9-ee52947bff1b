package user_handlers

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/services"
	utils2 "barber-api/pkg/utils"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"net/http"
)

// GetUserInfoHandler --
type GetUserInfoHandler struct {
	AdminDms *server.AdminDmsServer
}
type AdminUserResponse struct {
	Success bool              `json:"success,omitempty"`
	Message string            `json:"message,omitempty"`
	User    *dto.AdminUserDto `json:"user,omitempty"`
}

func (h *GetUserInfoHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	var err error
	// parse login from request body
	adminUserEmail := utils2.GetAdminUserEmailFromRequest(r)

	user, err := h.AdminDms.GetAdminUserByOptions(ctx, &services.AdminUserRequest{
		Email: adminUserEmail,
	})

	if err != nil {
		if utils2.IsNotFoundError(err) {
			uchiha_log.Logger().Info("Not found user", "user_email", adminUserEmail)
			transhttp.RespondJSON(w, http.StatusNotFound, AdminUserResponse{
				Success: false,
				Message: "Invalid request",
			})
			return
		}
		uchiha_log.Logger().Errorf("Error during get user by username %v", err)
		transhttp.RespondJSON(w, http.StatusNotFound, AdminUserResponse{
			Success: false,
			Message: err.Error(),
		})
		return
	}

	userDto := dto.ConvertAdminUserDto(user)

	transhttp.RespondJSON(w, http.StatusOK, AdminUserResponse{
		Success: true,
		User:    userDto,
	})
	return
}
