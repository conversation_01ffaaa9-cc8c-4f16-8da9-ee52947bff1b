package user_handlers

import (
	"fmt"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/protrip/uchiha-core/utils"
	"testing"
)

func Test_GenPassword(t *testing.T) {
	salt, hashedPassword, err := utils.GeneratePasswordToStoreDB("Admin@2025", true, "")
	if err != nil {
		uchiha_log.Logger().Error("Error when create password, details: ", err)
		return
	}
	fmt.Println("Salt: ", salt)
	fmt.Println("Hashed Password: ", hashedPassword)
}
