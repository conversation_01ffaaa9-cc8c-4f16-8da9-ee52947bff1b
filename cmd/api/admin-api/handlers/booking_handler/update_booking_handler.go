package booking_handler

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/utils"
	"encoding/json"
	"fmt"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
)

type UpdateBookingHandler struct {
}

var ignoreUpdateFields = []string{
	"id",
	"updated_at",
	"created_at",
	"customer_id",
	"staff_id",
	"booking_services",
}

var ignoreGetBookingColumns = []string{
	"created_at",
	"updated_at",
}

type UpdateBookingRequest struct {
	*models.Booking
}

func (h *UpdateBookingHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get booking ID from URL path parameter
	bookingId := utils.GetIntFromRequestParams(r, "booking_id")
	if bookingId <= 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Missing id",
		})
		return
	}

	// get booking update columns
	updateFields := utils.GetUpdateFields(r, ignoreUpdateFields)
	if len(updateFields) == 0 {
		transhttp.RespondJSON(w, http.StatusOK, dto.CommonResponseDto{
			Success: true,
		})
		return
	}

	var req UpdateBookingRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		uchiha_log.Logger().Warn("Error during parse UpdateBookingRequest: ", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid request body",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Get booking by id
	_, err = adminDms.GetBooking(ctx, &uchiha_models.CommonRequest{
		Id:            bookingId,
		IgnoreColumns: ignoreGetBookingColumns,
	})
	if err != nil {
		if !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error(fmt.Sprintf("Error when getting booking, err: %v", err))
			transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		transhttp.RespondJSON(w, http.StatusNotFound, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "Booking not found",
		})
		return
	}

	// Call update method
	_, err = adminDms.UpdateBooking(ctx, &uchiha_models.CommonRequest{
		Id:            bookingId,
		Model:         uchiha_orm.ToStruct(req.Booking),
		UpdateColumns: updateFields,
	})
	if err != nil {
		uchiha_log.Logger().Error(fmt.Sprintf("Error when updating booking, err: %v", err))
		transhttp.RespondJSON(w, http.StatusInternalServerError, dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Return success response
	res := &dto.CommonResponseDto{
		Success: true,
	}

	transhttp.RespondJSON(w, http.StatusOK, res)
}
