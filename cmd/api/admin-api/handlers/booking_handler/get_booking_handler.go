package booking_handler

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"barber-api/pkg/utils"
	"errors"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
	"sync"
	"time"
)

type GetBookingHandler struct {
}

func (h *GetBookingHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	startDateParam := r.URL.Query().Get("start")
	endDateParam := r.URL.Query().Get("end")

	// Define Vietnam location
	loc, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err != nil {
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "failed to load timezone",
		})
		return
	}

	var start, end time.Time
	start = time.Now().In(loc)
	end = start.AddDate(0, 0, 7)
	if startDateParam != "" {
		start, err = time.Parse("2006-01-02", startDateParam)
		if err != nil {
			transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: "invalid start date format, expected YYYY-MM-DD",
			})
			return
		}
	}
	if endDateParam != "" {
		end, err = time.Parse("2006-01-02", endDateParam)
		if err != nil {
			transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: "invalid end date format, expected YYYY-MM-DD",
			})
			return
		}
	}
	if end.Before(start) {
		end = start.AddDate(0, 0, 7)
	}

	maxEnd := start.AddDate(0, 0, 60)
	if end.After(maxEnd) {
		end = maxEnd
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	bookingRequest := &uchiha_models.CommonRequest{
		Page:  0,
		Limit: 5000,
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "date",
				Mode:  uchiha_orm.Gte,
				Value: start,
			},
			{
				Field: "date",
				Mode:  uchiha_orm.Lte,
				Value: end,
			},
		}),
		OrderBy: "date asc, time_period_name asc, id desc",
	}
	if r.URL.Query().Get("mode") == "latest" {
		bookingRequest = &uchiha_models.CommonRequest{
			Page:    0,
			Limit:   20,
			OrderBy: "id desc",
		}
	}

	bookings, err := adminDms.GetBookings(ctx, bookingRequest)
	if err != nil {
		if errors.Is(err, mysql.ErrNotFoundData) {
			transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
				Success:  true,
				Bookings: make([]*dto.BookingDto, 0),
			})
			return
		}
		uchiha_log.Logger().Error("Can not get bookings", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	serviceIds := make([]int64, 0)
	staffIds := make([]int64, 0)
	customerIds := make([]int64, 0)
	mapServices := make(map[int64]*models.Service, 0)
	mapStaff := make(map[int64]*models.Staff, 0)
	mapCustomers := make(map[int64]*models.Customer, 0)
	for _, booking := range bookings.List {
		for _, service := range booking.BookingServices {
			serviceIds = append(serviceIds, service.ServiceId)
		}
		staffIds = append(staffIds, booking.StaffId)
		customerIds = append(customerIds, booking.CustomerId)
	}

	wg := sync.WaitGroup{}
	wg.Add(3)
	var errChan = make(chan error, 3)
	go func() {
		defer wg.Done()
		services, err := adminDms.GetServices(ctx, &uchiha_models.CommonRequest{
			Ids:   utils.Unique(serviceIds),
			Limit: int64(len(serviceIds)),
		})
		if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
			uchiha_log.Logger().Error("Can not get services", "err", err.Error())
			errChan <- err
			return
		}
		for _, s := range services.List {
			mapServices[s.Id] = s
		}
	}()

	go func() {
		defer wg.Done()
		staffs, err := adminDms.GetStaffs(ctx, &uchiha_models.CommonRequest{
			Ids:   utils.Unique(staffIds),
			Limit: int64(len(staffIds)),
		})
		if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
			uchiha_log.Logger().Error("Can not get staffs", "err", err.Error())
			errChan <- err
			return
		}
		for _, s := range staffs.List {
			mapStaff[s.Id] = s
		}
	}()

	go func() {
		defer wg.Done()
		customers, err := adminDms.GetCustomers(ctx, &uchiha_models.CommonRequest{
			Ids:   utils.Unique(customerIds),
			Limit: int64(len(customerIds)),
		})
		if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
			uchiha_log.Logger().Error("Can not get customers", "err", err.Error())
			errChan <- err
			return
		}
		for _, c := range customers.List {
			mapCustomers[c.Id] = c
		}
	}()

	wg.Wait()
	close(errChan)
	if err := <-errChan; err != nil {
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	result := make([]*dto.BookingDto, 0)
	for _, booking := range bookings.List {
		bookingDto := dto.ConvertToBookingDto(booking)
		bookingDto.Stylist = mapStaff[booking.StaffId]
		bookingDto.Customer = mapCustomers[booking.CustomerId]
		for _, s := range booking.BookingServices {
			serviceDto := dto.ConvertToServiceDto(mapServices[s.ServiceId])
			if serviceDto == nil {
				continue
			}
			// override the service custom price
			serviceDto.Price = s.Price
			bookingDto.Services = append(bookingDto.Services, serviceDto)
		}
		result = append(result, bookingDto)
	}

	transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
		Success:  true,
		Bookings: result,
	})
}
