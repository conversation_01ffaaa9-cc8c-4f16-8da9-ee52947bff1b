package main

import (
	"barber-api/cmd/api/admin-api/handlers/analytics_handler"
	"barber-api/cmd/api/admin-api/handlers/booking_handler"
	customer "barber-api/cmd/api/admin-api/handlers/customer_handlers"
	service "barber-api/cmd/api/admin-api/handlers/service_handlers"
	staff "barber-api/cmd/api/admin-api/handlers/staff_handlers"
	user_handlers "barber-api/cmd/api/admin-api/handlers/users_handler"
	"barber-api/cmd/dms/server"
	"barber-api/pkg/auth"
	"context"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/protrip/uchiha-core/web/middlewares"
	"net/http"
)

type api struct {
	name           string
	basePath       string
	adminDmsServer *server.AdminDmsServer
	redisClient    *redis_client.RedisClient
}

func (s *api) BasePath() string {
	return s.basePath
}

func NewAdminApi(name, basePath string) *api {
	return &api{
		name:     name,
		basePath: basePath,
	}
}

func (s *api) Init() error {
	adminDmsServer := server.NewAdminDmsServer()
	err := object_provider.Provide(adminDmsServer)
	if err != nil {
		uchiha_log.Logger().Fatal("Can not register dms", "err", err.Error())
	}

	s.adminDmsServer = adminDmsServer

	redisClient := redis_client.NewRedisClient(context.Background(), nil)
	err = object_provider.Provide(redisClient)
	if err != nil {
		uchiha_log.Logger().Fatal("Can not register redis", "err", err.Error())
	}
	s.redisClient = redisClient
	return nil
}

func (s *api) Name() string {
	return s.name
}

func (s *api) Routes() transhttp.Routes {
	routes := transhttp.Routes{}
	routes = append(routes, s.InitUserRoutes(s.basePath)...)
	routes = append(routes, s.InitCustomerRoutes(s.basePath)...)
	routes = append(routes, s.InitServiceRoutes(s.basePath)...)
	routes = append(routes, s.InitStaffRoutes(s.basePath)...)
	routes = append(routes, s.InitBookingRoutes(s.basePath)...)
	routes = append(routes, s.InitAnalyticsRoutes(s.basePath)...)
	return routes
}

// Dont use this routes as reference
func (s *api) InitUserRoutes(basePath string) transhttp.Routes {
	authInfo := transhttp.AuthInfo{
		Middleware: middlewares.NewAuthMiddleware(transhttp.AuthInfo{
			Enable:    true,
			TokenType: auth.TokenTypeAdminUserAccessToken,
		}, s.redisClient, auth.TokenDefinitions),
	}
	return transhttp.Routes{
		transhttp.Route{
			Name:     "Signin",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/user/login",
			Handler: &user_handlers.CredentialsAuthHandler{
				Redis: s.redisClient,
				UserHelper: &auth.UserHelper{
					AdminDms: s.adminDmsServer,
				},
				TokenType: auth.TokenTypeAdminUserAccessToken,
				UserType:  auth.UserTypeAdminUser,
			},
		},
		transhttp.Route{
			Name:     "Change password",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/user/change-password",
			AuthInfo: authInfo,
			Handler: &user_handlers.CredentialsChangePasswordHandler{
				BkRedis: s.redisClient,
				UserHelper: &auth.UserHelper{
					AdminDms: s.adminDmsServer,
				},
				TokenType: auth.TokenTypeAdminUserAccessToken,
				UserType:  auth.UserTypeAdminUser,
			},
		},
		transhttp.Route{
			Name:     "Get user info",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/user",
			AuthInfo: authInfo,
			Handler: &user_handlers.GetUserInfoHandler{
				AdminDms: s.adminDmsServer,
			},
		},
	}
}

func (s *api) InitCustomerRoutes(basePath string) transhttp.Routes {
	authInfo := transhttp.AuthInfo{
		Middleware: middlewares.NewAuthMiddleware(transhttp.AuthInfo{
			Enable:    true,
			TokenType: auth.TokenTypeAdminUserAccessToken,
		}, s.redisClient, auth.TokenDefinitions),
	}
	return transhttp.Routes{
		transhttp.Route{
			Name:     "get customer",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/customers",
			Handler:  &customer.GetCustomersHandler{},
			AuthInfo: authInfo,
		},
	}
}

func (s *api) InitServiceRoutes(basePath string) transhttp.Routes {
	authInfo := transhttp.AuthInfo{
		Middleware: middlewares.NewAuthMiddleware(transhttp.AuthInfo{
			Enable:    true,
			TokenType: auth.TokenTypeAdminUserAccessToken,
		}, s.redisClient, auth.TokenDefinitions),
	}
	return transhttp.Routes{
		transhttp.Route{
			Name:     "get service",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/services",
			Handler:  &service.GetServicesHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "get service",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/services/{id:(?:\\d+)}",
			Handler:  &service.GetServiceHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "create services",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/services",
			Handler:  &service.CreateServicesHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "update services",
			Method:   http.MethodPut,
			BasePath: basePath,
			Pattern:  "/services/{id:(?:\\d+)}",
			Handler:  &service.UpdateServiceHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "delete services",
			Method:   http.MethodDelete,
			BasePath: basePath,
			Pattern:  "/services/{id:(?:\\d+)}",
			Handler:  &service.DeleteServiceHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "update services custom",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/services/{id:(?:\\d+)}/customs/{staff_id:(?:\\d+)}",
			Handler:  &service.UpdateServiceCustomHandler{},
			AuthInfo: authInfo,
		},
	}
}

func (s *api) InitBookingRoutes(basePath string) transhttp.Routes {
	authInfo := transhttp.AuthInfo{
		Middleware: middlewares.NewAuthMiddleware(transhttp.AuthInfo{
			Enable:    true,
			TokenType: auth.TokenTypeAdminUserAccessToken,
		}, s.redisClient, auth.TokenDefinitions),
	}
	return transhttp.Routes{
		transhttp.Route{
			Name:     "get bookings",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/bookings",
			Handler:  &booking_handler.GetBookingHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "paid booking",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/bookings/{booking_id:(?:\\d+)}/finish",
			Handler:  &booking_handler.BookingToTransactionHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "update booking",
			Method:   http.MethodPut,
			BasePath: basePath,
			Pattern:  "/bookings/{booking_id:(?:\\d+)}",
			Handler:  &booking_handler.UpdateBookingHandler{},
		},
	}
}

func (s *api) InitStaffRoutes(basePath string) transhttp.Routes {
	authInfo := transhttp.AuthInfo{
		Middleware: middlewares.NewAuthMiddleware(transhttp.AuthInfo{
			Enable:    true,
			TokenType: auth.TokenTypeAdminUserAccessToken,
		}, s.redisClient, auth.TokenDefinitions),
	}
	return transhttp.Routes{
		transhttp.Route{
			Name:     "get staff",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/staff",
			Handler:  &staff.GetStaffHandler{},
			AuthInfo: authInfo,
		},
		// Add this to your InitStaffRoutes function
		transhttp.Route{
			Name:     "get staff by id",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/staff/{id:(?:\\d+)}",
			Handler:  &staff.GetStaffByIdHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "update staff",
			Method:   http.MethodPut,
			BasePath: basePath,
			Pattern:  "/staff/{id:(?:\\d+)}",
			Handler:  &staff.UpdateStaffHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "create staff",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/staff",
			Handler:  &staff.CreateStaffHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "delete staff",
			Method:   http.MethodDelete,
			BasePath: basePath,
			Pattern:  "/staff/{id:(?:\\d+)}",
			Handler:  &staff.DeleteStaffHandler{},
			AuthInfo: authInfo,
		},
		transhttp.Route{
			Name:     "update staff off",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/staff/{id:(?:\\d+)}/staff-off",
			Handler:  &staff.UpdateStaffOffHandler{},
			AuthInfo: authInfo,
		},
	}
}

func (s *api) InitAnalyticsRoutes(basePath string) transhttp.Routes {
	authInfo := transhttp.AuthInfo{
		Middleware: middlewares.NewAuthMiddleware(transhttp.AuthInfo{
			Enable:    true,
			TokenType: auth.TokenTypeAdminUserAccessToken,
		}, s.redisClient, auth.TokenDefinitions),
	}
	return transhttp.Routes{
		transhttp.Route{
			Name:     "get analytics",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/analytics",
			Handler:  &analytics_handler.AnalyticsHandler{},
			AuthInfo: authInfo,
		},
	}
}
