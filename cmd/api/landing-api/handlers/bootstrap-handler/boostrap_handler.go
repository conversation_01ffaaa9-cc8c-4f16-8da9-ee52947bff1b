package bootstrap_handler

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	"context"
	"encoding/json"
	"errors"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"net/http"
	"sync"
	"time"
)

type BoostrapApiHandler struct {
}

func (h *BoostrapApiHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get Redis and DMS clients
	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply redis client", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	var services *models.Services
	var staffs *models.Staffs
	var serviceCustoms *models.ServiceCustoms
	var staffOffs *models.StaffOffs
	wg := sync.WaitGroup{}
	wg.Add(4)
	var errChan = make(chan error, 4)

	go func() {
		defer wg.Done()
		services, err = getAvailableServices(ctx, redisClient, adminDms)
		if err != nil {
			uchiha_log.Logger().Error("Error getting available services", "err", err.Error())
			errChan <- err
			return
		}
	}()

	go func() {
		defer wg.Done()
		staffs, err = getAvailableStaffs(ctx, redisClient, adminDms)
		if err != nil {
			uchiha_log.Logger().Error("Error getting available staffs", "err", err.Error())
			errChan <- err
			return
		}
	}()

	go func() {
		defer wg.Done()
		serviceCustoms, err = getServiceCustoms(ctx, redisClient, adminDms)
		if err != nil {
			uchiha_log.Logger().Error("Error getting service customs", "err", err.Error())
			errChan <- err
			return
		}
	}()

	go func() {
		defer wg.Done()
		staffOffs, err = getStaffOffs(ctx, redisClient, adminDms)
		if err != nil {
			uchiha_log.Logger().Error("Error getting staff offs", "err", err.Error())
			errChan <- err
			return
		}
	}()

	wg.Wait()
	close(errChan)
	if err := <-errChan; err != nil {
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}
	mapStaff := make(map[int64]bool, 0)
	for _, staff := range staffs.List {
		mapStaff[staff.Id] = true
	}
	finalServiceCustom := make([]*models.ServiceCustom, 0)
	for _, serviceCustom := range serviceCustoms.List {
		if mapStaff[serviceCustom.StaffId] || serviceCustom.StaffId < 1 {
			finalServiceCustom = append(finalServiceCustom, serviceCustom)
		}
	}

	transhttp.RespondJSON(w, http.StatusOK, &dto.LandingResponse{
		Success:        true,
		Stylists:       dto.ConvertListStaffToStaffDto(staffs.List),
		Services:       dto.ConvertListServiceToServiceDto(services.List),
		ServiceCustoms: dto.ConvertListServiceCustomToServiceCustomDto(finalServiceCustom),
		StylistOff:     dto.ConvertListStaffOffToStaffOffDto(staffOffs.List),
	})
	return
}

// getAvailableServices gets available services with Redis caching
func getAvailableServices(ctx context.Context, redisClient *redis_client.RedisClient, adminDms *server.AdminDmsServer) (*models.Services, error) {
	// Try to get from Redis first
	cacheKey := "bootstrap:services"
	cachedData, err := redisClient.Get(ctx, cacheKey).Result()
	if err == nil && cachedData != "" {
		// Cache hit
		var services models.Services
		if err := json.Unmarshal([]byte(cachedData), &services); err == nil {
			return &services, nil
		}
	}

	// Cache miss or error, fetch from DMS
	services, err := adminDms.GetServices(ctx, &uchiha_models.CommonRequest{
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "is_available",
				Mode:  uchiha_orm.Eq,
				Value: true,
			},
		}),
		OrderBy: "updated_at desc",
		Limit:   1000,
	})
	if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
		return nil, err
	}

	// Cache the result
	if services != nil && len(services.List) > 0 {
		if data, err := json.Marshal(services); err == nil {
			redisClient.Set(ctx, cacheKey, data, 30*time.Minute)
		}
	}

	return services, nil
}

// getAvailableStaffs gets available staffs with Redis caching
func getAvailableStaffs(ctx context.Context, redisClient *redis_client.RedisClient, adminDms *server.AdminDmsServer) (*models.Staffs, error) {
	// Try to get from Redis first
	cacheKey := "bootstrap:staffs"
	cachedData, err := redisClient.Get(ctx, cacheKey).Result()
	if err == nil && cachedData != "" {
		// Cache hit
		var staffs models.Staffs
		if err := json.Unmarshal([]byte(cachedData), &staffs); err == nil {
			return &staffs, nil
		}
	}

	// Cache miss or error, fetch from DMS
	staffs, err := adminDms.GetStaffs(ctx, &uchiha_models.CommonRequest{
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "is_available",
				Mode:  uchiha_orm.Eq,
				Value: true,
			},
		}),
		OrderBy: "updated_at desc",
		Limit:   1000,
	})
	if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
		return nil, err
	}

	// Cache the result
	if staffs != nil && len(staffs.List) > 0 {
		if data, err := json.Marshal(staffs); err == nil {
			redisClient.Set(ctx, cacheKey, data, 30*time.Minute)
		}
	}

	return staffs, nil
}

// getServiceCustoms gets service customs for available services with Redis caching
func getServiceCustoms(ctx context.Context, redisClient *redis_client.RedisClient, adminDms *server.AdminDmsServer) (*models.ServiceCustoms, error) {
	// Try to get from Redis first
	cacheKey := "bootstrap:service_customs"
	cachedData, err := redisClient.Get(ctx, cacheKey).Result()
	if err == nil && cachedData != "" {
		// Cache hit
		var serviceCustoms models.ServiceCustoms
		if err := json.Unmarshal([]byte(cachedData), &serviceCustoms); err == nil {
			return &serviceCustoms, nil
		}
	}

	serviceCustoms, err := adminDms.GetServiceCustoms(ctx, &uchiha_models.CommonRequest{
		TablePrefix: "sc",
		JoinTables: []*uchiha_models.JoinTable{
			{
				Name:   "services",
				Prefix: "s",
				On:     "s.id = sc.service_id and s.is_available = true",
			},
		},
		Limit: 1000,
	})
	if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
		return nil, err
	}

	// Cache the result
	if serviceCustoms != nil && len(serviceCustoms.List) > 0 {
		if data, err := json.Marshal(serviceCustoms); err == nil {
			redisClient.Set(ctx, cacheKey, data, 30*time.Minute)
		}
	}

	return serviceCustoms, nil
}

// getStaffOffs gets staff offs for available staffs with Redis caching
func getStaffOffs(ctx context.Context, redisClient *redis_client.RedisClient, adminDms *server.AdminDmsServer) (*models.StaffOffs, error) {
	// Try to get from Redis first
	cacheKey := "bootstrap:staff_offs"
	cachedData, err := redisClient.Get(ctx, cacheKey).Result()
	if err == nil && cachedData != "" {
		// Cache hit
		var staffOffs models.StaffOffs
		if err := json.Unmarshal([]byte(cachedData), &staffOffs); err == nil {
			return &staffOffs, nil
		}
	}

	vietnamLoc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	// Get current date
	now := time.Now().In(vietnamLoc)
	today := now.Format("2006-01-02")

	// Get staff offs for the next 30 days
	endDate := now.AddDate(0, 0, 60).In(vietnamLoc).Format("2006-01-02")

	staffOffs, err := adminDms.GetStaffOffs(ctx, &uchiha_models.CommonRequest{
		CustomizedSelect: "so.id, so.staff_id, TO_CHAR(so.date :: DATE, 'yyyy-mm-dd') as date, so.time_period_name",
		TablePrefix:      "so",
		JoinTables: []*uchiha_models.JoinTable{
			{
				Name:   "staff",
				Prefix: "s",
				On:     "s.id = so.staff_id and s.is_available = true",
			},
		},
		IgnoreColumns: []string{"created_at", "updated_at"},
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "date",
				Mode:  uchiha_orm.Gte,
				Value: today,
			},
			{
				Field: "date",
				Mode:  uchiha_orm.Lte,
				Value: endDate,
			},
		}),
		Limit: 1000,
	})
	if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
		return nil, err
	}

	// Cache the result
	if staffOffs != nil && len(staffOffs.List) > 0 {
		if data, err := json.Marshal(staffOffs); err == nil {
			redisClient.Set(ctx, cacheKey, data, 30*time.Minute)
		}
	}

	return staffOffs, nil
}
