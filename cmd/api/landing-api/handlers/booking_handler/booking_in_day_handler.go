package booking_handler

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/services"
	"barber-api/pkg/utils"
	"context"
	"errors"
	"fmt"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"net/http"
	"strings"
	"time"
)

type BookingInDayHandler struct {
}

func (h *BookingInDayHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	date := r.URL.Query().Get("date")
	dateParsed, err := time.Parse("2006-01-02", date)
	if err != nil {
		transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid_date_format",
		})
		return
	}
	vietnamLoc, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	currentDay := time.Now().Truncate(24 * time.Hour).In(vietnamLoc)
	if dateParsed.Before(currentDay) {
		transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "invalid_date",
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply redis", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	redisResult, err := redisClient.HGetAll(ctx, "bid:"+date).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		uchiha_log.Logger().Error("Can not get booking in day redis", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	if errors.Is(err, redis.Nil) || len(redisResult) < 1 {
		result, err := GetBookingInDay(ctx, adminDms, redisClient, date)
		if err != nil {
			return
		}
		transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
			Success:      true,
			BookingInDay: result,
		})
		return
	}
	go func() {
		now := time.Now().Unix()
		if lastRefresh := cast.ToInt64(redisResult["refresh"]); now-lastRefresh >= 3600 {
			_, rerr := GetBookingInDay(context.Background(), adminDms, redisClient, date)
			if rerr != nil {
				uchiha_log.Logger().Error("Can not refresh booking in day", "err", rerr.Error())
			}
		}

	}()

	result := make(map[string]map[int64]int64)
	for k, count := range redisResult {
		if k == "refresh" {
			continue
		}
		split := strings.Split(k, "_")
		if len(split) != 2 {
			continue
		}
		staffId := cast.ToInt64(split[0])
		timePeriodName := split[1]
		if _, ok := result[timePeriodName]; !ok {
			result[timePeriodName] = make(map[int64]int64)
		}
		result[timePeriodName][staffId] = cast.ToInt64(count)
	}

	transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
		Success:      true,
		BookingInDay: result,
	})
	return
}

func GetBookingInDay(ctx context.Context, adminDms *server.AdminDmsServer, redisClient *redis_client.RedisClient, date string) (map[string]map[int64]int64, error) {
	bookingInDay, err := adminDms.GetBookingInDay(ctx, &services.BookingRequest{
		Date: date,
	})
	if err != nil {
		uchiha_log.Logger().Error("Can not get booking in day", "err", err.Error())
		return nil, err
	}
	result := make(map[string]map[int64]int64)

	for _, bid := range bookingInDay.List {
		if bid.Count < 1 {
			continue
		}
		if _, ok := result[bid.TimePeriodName]; !ok {
			result[bid.TimePeriodName] = make(map[int64]int64)
		}
		result[bid.TimePeriodName][bid.StaffId] = bid.Count
		utils.DoSomethingWithRetry(func() (interface{}, error) {
			rerr := redisClient.HSet(ctx, "bid:"+date, fmt.Sprintf("%v_%v", bid.StaffId, bid.TimePeriodName), bid.Count).Err()
			return rerr == nil, rerr
		}, 50, "set redis value")
	}
	utils.DoSomethingWithRetry(func() (interface{}, error) {
		rerr := redisClient.HSet(ctx, "bid:"+date, "refresh", time.Now().Unix()).Err()
		return rerr == nil, rerr
	}, 50, "set redis value")
	utils.DoSomethingWithRetry(func() (interface{}, error) {
		rerr := redisClient.Expire(ctx, "bid:"+date, 24*time.Hour).Err()
		return rerr == nil, rerr
	}, 50, "expire redis value")
	return result, nil
}

func ConvertMapStringToMapInt64(m map[string]string) map[string]int64 {
	result := make(map[string]int64)
	for k, v := range m {
		result[k] = cast.ToInt64(v)
	}
	return result
}
