package booking_handler

import (
	"barber-api/cmd/dms/server"
	"barber-api/dto"
	"barber-api/exmsg/models"
	fb "barber-api/pkg/facebook"
	"barber-api/pkg/notify"
	"barber-api/pkg/telegram"
	"barber-api/pkg/utils"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	utils2 "github.com/protrip/uchiha-core/utils"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"net/http"
	"regexp"
	"time"
)

type CreateBookingHandler struct {
}

type CreateBookingRequest struct {
	CustomerPhoneNumber string  `json:"customer_phone_number"`
	CustomerName        string  `json:"customer_name"`
	Date                string  `json:"date"`
	TimePeriodName      string  `json:"time_period_name"`
	Stylist             int64   `json:"stylist"`
	Services            []int64 `json:"services"`
	Note                string  `json:"note"`
}

var (
	ErrEmptyPhoneNumber   = errors.New("phone_number_required")
	ErrInvalidPhoneNumber = errors.New("invalid_phone_number")
	ErrEmptyDate          = errors.New("date_required")
	ErrInvalidDateFormat  = errors.New("invalid_date_format")
	ErrEmptyTimePeriod    = errors.New("time_period_required")
	ErrInvalidTimePeriod  = errors.New("invalid_time_period_format")
	ErrPastDateTime       = errors.New("booking_time_in_past")
	ErrEmptyStylist       = errors.New("stylist_required")
	ErrEmptyServices      = errors.New("services_required")
)

func (h *CreateBookingHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse request
	decoder := json.NewDecoder(r.Body)
	req := &CreateBookingRequest{}
	if err := decoder.Decode(req); err != nil {
		uchiha_log.Logger().Error("Error while parse request body", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}
	// Validate request
	if err := ValidateCreateBookingRequest(req); err != nil {
		transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	adminDms, err := object_provider.Supply[server.AdminDmsServer]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply dms", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply redis", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	// Check if time period exists in the database
	timePeriods, err := adminDms.GetTimePeriods(ctx, &uchiha_models.CommonRequest{
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "name",
				Mode:  uchiha_orm.Eq,
				Value: req.TimePeriodName,
			},
		}),
		Limit: 1,
	})

	if err != nil {
		uchiha_log.Logger().Error("Error checking time period", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	if len(timePeriods.List) == 0 {
		transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: "time_period_not_found",
		})
		return
	}

	// Get or create customer by phone number
	customer, err := adminDms.GetCustomer(ctx, &uchiha_models.CommonRequest{
		Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
			{
				Field: "phone_number",
				Mode:  uchiha_orm.Eq,
				Value: req.CustomerPhoneNumber,
			},
		}),
	})

	var customerId int64
	if err != nil {
		if errors.Is(err, mysql.ErrNotFoundData) {
			// Create new customer
			customerRes, err := adminDms.InsertCustomer(ctx, &uchiha_models.CommonRequest{
				Model: uchiha_orm.ToStruct(&models.Customer{
					Name:        req.CustomerName,
					PhoneNumber: req.CustomerPhoneNumber,
				}),
			})
			if err != nil {
				uchiha_log.Logger().Error("Error creating customer", "err", err.Error())
				transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
					Success:      false,
					ErrorMessage: err.Error(),
				})
				return
			}
			customerId = customerRes.LastId
		} else {
			uchiha_log.Logger().Error("Error getting customer", "err", err.Error())
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
	} else {
		customerId = customer.Id
	}

	var stylist *models.Staff
	if req.Stylist > 0 {
		stylist, err = adminDms.GetStaff(ctx, &uchiha_models.CommonRequest{
			Id: req.Stylist,
			Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
				{
					Field: "is_available",
					Mode:  uchiha_orm.Eq,
					Value: true,
				},
			}),
		})
		if err != nil {
			if utils.IsNotFoundError(err) {
				transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
					Success:      false,
					ErrorMessage: "stylist_not_found",
				})
				return
			}
			uchiha_log.Logger().Error("Error getting stylist", "err", err.Error())
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		staffOffs, err := adminDms.GetStaffOffs(ctx, &uchiha_models.CommonRequest{
			Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
				{
					Field: "staff_id",
					Mode:  uchiha_orm.Eq,
					Value: req.Stylist,
				},
				{
					Field: "date",
					Mode:  uchiha_orm.Eq,
					Value: req.Date,
				},
				{
					Field: "time_period_name",
					Mode:  uchiha_orm.Eq,
					Value: req.TimePeriodName,
				},
			}),
		})
		if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
			uchiha_log.Logger().Error("Error checking staff availability", "err", err.Error())
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		if staffOffs != nil && len(staffOffs.List) > 0 {
			transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: "stylist_not_available",
			})
			return
		}
	}

	bookingServices := make([]*models.BookingService, 0)
	if len(req.Services) > 0 {
		servicesIds := utils.Unique(req.Services)
		mapServices := make(map[int64]*models.Service, 0)

		// Check if all services are available
		services, err := adminDms.GetServices(ctx, &uchiha_models.CommonRequest{
			Ids: servicesIds,
			Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
				{
					Field: "is_available",
					Mode:  uchiha_orm.Eq,
					Value: true,
				},
			}),
			Limit: int64(len(req.Services)),
		})
		if err != nil {
			uchiha_log.Logger().Error("Error getting services", "err", err.Error())
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}

		// Check if all requested services exist and are available
		if len(services.List) != len(servicesIds) {
			transhttp.RespondJSON(w, http.StatusBadRequest, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: "invalid_services",
			})
			return
		}

		for _, service := range services.List {
			mapServices[service.Id] = service
		}
		serviceCustoms, err := adminDms.GetServiceCustoms(ctx, &uchiha_models.CommonRequest{
			Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
				{
					Field: "service_id",
					Mode:  uchiha_orm.Eq,
					Value: servicesIds,
				},
				{
					Field: "time_period_name",
					Mode:  uchiha_orm.Eq,
					Value: req.TimePeriodName,
				},
			}),
		})
		if err != nil && !errors.Is(err, mysql.ErrNotFoundData) {
			uchiha_log.Logger().Error("Error getting service customs", "err", err.Error())
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		mapServicesCustom := make(map[string]*models.ServiceCustom, 0) // map[staffId_serviceId]ServiceCustom
		for _, serviceCustom := range serviceCustoms.List {
			mapServicesCustom[fmt.Sprintf("%d_%d", serviceCustom.StaffId, serviceCustom.ServiceId)] = serviceCustom
		}

		for _, serviceId := range req.Services {
			bookingService := &models.BookingService{
				ServiceId: serviceId,
				Price:     mapServices[serviceId].Price,
			}
			if serviceCustom, ok := mapServicesCustom[fmt.Sprintf("0_%d", serviceId)]; ok {
				bookingService.ServiceCustomId = serviceCustom.Id
				bookingService.Price = serviceCustom.Price
			}
			if serviceCustom, ok := mapServicesCustom[fmt.Sprintf("%d_%d", req.Stylist, serviceId)]; ok {
				bookingService.ServiceCustomId = serviceCustom.Id
				bookingService.Price = serviceCustom.Price
			}

			bookingServices = append(bookingServices, bookingService)
			continue
		}
	}

	isCheck := cast.ToBool(r.URL.Query().Get("check"))
	if isCheck {
		exitsBookings, err := adminDms.GetBookings(ctx, &uchiha_models.CommonRequest{
			Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
				{
					Field: "staff_id",
					Mode:  uchiha_orm.Eq,
					Value: req.Stylist,
				},
				{
					Field: "date",
					Mode:  uchiha_orm.Eq,
					Value: req.Date,
				},
				{
					Field: "time_period_name",
					Mode:  uchiha_orm.Eq,
					Value: req.TimePeriodName,
				},
			}),
			IgnoreColumns: utils2.GetIgnoreColumnsFromEnableColumns(&models.Booking{}, []string{"id"}),
		})
		if err != nil && !utils.IsNotFoundError(err) {
			uchiha_log.Logger().Error("Error getting booking", "err", err.Error())
			transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: err.Error(),
			})
			return
		}
		if len(exitsBookings.List) > 0 {
			transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
				Success:      false,
				ErrorMessage: "booking_already_exists",
				BookingInDay: map[string]map[int64]int64{
					req.TimePeriodName: {
						req.Stylist: int64(len(exitsBookings.List)),
					},
				},
			})
			return
		}
	}

	// Create booking
	ignoreColumns := make([]string, 0)
	if len(bookingServices) < 1 {
		ignoreColumns = append(ignoreColumns, "booking_services")
	}
	_, err = adminDms.InsertBooking(ctx, &uchiha_models.CommonRequest{
		Model: uchiha_orm.ToStruct(&models.Booking{
			CustomerId:      customerId,
			StaffId:         req.Stylist,
			Date:            req.Date,
			TimePeriodName:  req.TimePeriodName,
			Note:            req.Note,
			BookingServices: bookingServices,
			Status:          "queued",
		}),
		IgnoreColumns: ignoreColumns,
	})
	if err != nil {
		uchiha_log.Logger().Error("Error creating booking", "err", err.Error())
		transhttp.RespondJSON(w, http.StatusInternalServerError, &dto.CommonResponseDto{
			Success:      false,
			ErrorMessage: err.Error(),
		})
		return
	}

	utils.DoSomethingWithRetry(func() (interface{}, error) {
		val, rerr := redisClient.HGet(ctx, "bid:"+req.Date, "refresh").Result()
		if (err != nil && errors.Is(err, redis.Nil)) || cast.ToInt64(val) < 1 {
			return true, nil
		}
		rerr = redisClient.HIncrBy(ctx, "bid:"+req.Date, fmt.Sprintf("%v_%v", req.Stylist, req.TimePeriodName), 1).Err()
		return rerr == nil, rerr
	}, 50, "expire redis value")

	// send message after booking success
	go sendBookingNotifications(req, stylist)

	transhttp.RespondJSON(w, http.StatusOK, &dto.CommonResponseDto{
		Success: true,
	})
}

// ValidateCreateBookingRequest validates the CreateBookingRequest
func ValidateCreateBookingRequest(req *CreateBookingRequest) error {
	// Validate phone number
	if req.CustomerPhoneNumber == "" {
		return ErrEmptyPhoneNumber
	}

	// Validate phone number format (simple validation)
	phoneRegex := regexp.MustCompile(`^[0-9]{10,11}$`)
	if !phoneRegex.MatchString(req.CustomerPhoneNumber) {
		return ErrInvalidPhoneNumber
	}

	// Validate date
	if req.Date == "" {
		return ErrEmptyDate
	}

	// Validate date format (YYYY-MM-DD) by parsing it
	_, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return ErrInvalidDateFormat
	}

	// Validate time period
	if req.TimePeriodName == "" {
		return ErrEmptyTimePeriod
	}

	// Validate time period format (HH:MM) by parsing it
	_, err = time.Parse("15:04", req.TimePeriodName)
	if err != nil {
		return ErrInvalidTimePeriod
	}

	// Validate booking is not in the past
	vietnamLoc, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err == nil {
		now := time.Now().In(vietnamLoc)
		bookingDate, err := time.ParseInLocation("2006-01-02 15:04", req.Date+" "+req.TimePeriodName, vietnamLoc)
		if err == nil && bookingDate.Before(now) {
			return ErrPastDateTime
		}
	}

	return nil
}

func sendBookingNotifications(req *CreateBookingRequest, stylist *models.Staff) {
	message := buildBookingMessage(req, stylist)

	go func() {
		for _, notifier := range getActiveNotifiers() {
			go func() {
				if err := notifier.Send(message); err != nil {
					uchiha_log.Logger().Error("Send notification failed", "notifier", fmt.Sprintf("%T", notifier), "err", err.Error())
				}
			}()
		}
	}()
}

func buildBookingMessage(req *CreateBookingRequest, stylist *models.Staff) string {
	customerName := req.CustomerName
	if customerName == "" {
		customerName = req.CustomerPhoneNumber
	} else {
		customerName = fmt.Sprintf("%v(%v)", customerName, req.CustomerPhoneNumber)
	}

	bookingDate, _ := time.Parse("2006-01-02 15:04", req.Date+" "+req.TimePeriodName)
	message := fmt.Sprintf("Khách hàng %v vừa đặt lịch vào %v", customerName, bookingDate.Format("02/01/2006 15:04"))

	if stylist != nil {
		message += fmt.Sprintf(" cho stylist %v.", stylist.Name)
	}

	return message
}

func getActiveNotifiers() []notify.Notifier {
	var notifiers []notify.Notifier

	if fbHandler, err := object_provider.Supply[fb.FBHandler](); err == nil {
		notifiers = append(notifiers, notify.NewFacebookNotifier(fbHandler))
	} else {
		uchiha_log.Logger().Error("Error getting fb handler", "err", err.Error())
	}

	if tgHandler, err := object_provider.Supply[telegram.TelegramHandler](); err == nil {
		notifiers = append(notifiers, notify.NewTelegramNotifier(tgHandler))
	} else {
		uchiha_log.Logger().Error("Error getting telegram handler", "err", err.Error())
	}

	return notifiers
}
