package main

import (
	"barber-api/cmd/api/landing-api/handlers/booking_handler"
	"barber-api/cmd/api/landing-api/handlers/bootstrap-handler"
	"barber-api/cmd/dms/server"
	"barber-api/pkg/telegram"
	"context"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"net/http"
)

type api struct {
	name           string
	basePath       string
	adminDmsServer *server.AdminDmsServer
	redisClient    *redis_client.RedisClient
}

func (s *api) BasePath() string {
	return s.basePath
}

func NewLandingApi(name, basePath string) *api {
	return &api{
		name:     name,
		basePath: basePath,
	}
}

func (s *api) Init() error {
	adminDmsServer := server.NewAdminDmsServer()
	err := object_provider.Provide(adminDmsServer)
	if err != nil {
		uchiha_log.Logger().Fatal("Can not register dms", "err", err.Error())
	}

	s.adminDmsServer = adminDmsServer

	redisClient := redis_client.NewRedisClient(context.Background(), nil)
	err = object_provider.Provide(redisClient)
	if err != nil {
		uchiha_log.Logger().Fatal("Can not register redis", "err", err.Error())
	}
	s.redisClient = redisClient

	tgHandler, err := telegram.NewTelegramHandler()
	if err != nil {
		uchiha_log.Logger().Fatal("Can not init telegram handler", "err", err.Error())
	}

	err = object_provider.Provide(tgHandler)
	if err != nil {
		uchiha_log.Logger().Fatal("Can not register telegram handler", "err", err.Error())
	}

	return nil
}

func (s *api) Name() string {
	return s.name
}

func (s *api) Routes() transhttp.Routes {
	routes := transhttp.Routes{}
	routes = append(routes, s.InitBookingRoutes(s.basePath)...)
	routes = append(routes, s.InitBoostrapRoutes(s.basePath)...)
	return routes
}

func (s *api) InitBookingRoutes(basePath string) transhttp.Routes {
	return transhttp.Routes{
		transhttp.Route{
			Name:     "create booking",
			Method:   http.MethodPost,
			BasePath: basePath,
			Pattern:  "/bookings",
			Handler:  &booking_handler.CreateBookingHandler{},
		},
		transhttp.Route{
			Name:     "create booking",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/bookings/in-day",
			Handler:  &booking_handler.BookingInDayHandler{},
		},
	}
}

func (s *api) InitBoostrapRoutes(basePath string) transhttp.Routes {
	return transhttp.Routes{
		transhttp.Route{
			Name:     "Boostrap",
			Method:   http.MethodGet,
			BasePath: basePath,
			Pattern:  "/bootstrap",
			Handler:  &bootstrap_handler.BoostrapApiHandler{},
		},
	}
}
