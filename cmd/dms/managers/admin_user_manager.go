package manager

import (
	"barber-api/cmd/dms/table"
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"github.com/protrip/uchiha-core/utils/dms_utils"
)

type AdminUserManager struct {
	mysql *mysql.Mysql
}

func NewAdminUserManager(mysql *mysql.Mysql) *AdminUserManager {
	return &AdminUserManager{mysql: mysql}
}

func (c AdminUserManager) InsertAdminUser(ctx context.Context, req *services.AdminUserRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool := mysql.NewInsert(ctx, c.mysql, table.GetAdminUserTable(nil), req.Model)
	// Build query
	query, args, err := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList()).
		PlaceholderFormat(squirrel.Dollar).
		Values(sqlTool.GetFilledValues()...).
		ToSql()

	if err != nil {
		return nil, err
	}

	// Perform insert
	result, err := sqlTool.Insert(query, args...)
	if err != nil {
		if dms_utils.IsDuplicateError(err) {
			return nil, mysql.ErrDuplicateData
		}
		if dms_utils.IsDeadlockError(err) {
			return nil, mysql.ErrDeadlock
		}
		return nil, err
	}

	rows, _ := result.RowsAffected()
	lastId, _ := result.LastInsertId()

	return &uchiha_models.CommonResponse{
		Status:       true,
		RowsAffected: rows,
		LastId:       lastId,
	}, err
}

func (c AdminUserManager) GetAdminUserByOptions(ctx context.Context, req *services.AdminUserRequest) (*models.AdminUser, error) {
	AdminUser := &models.AdminUser{}
	sqlTool := mysql.NewSelect(ctx, c.mysql, table.GetAdminUserTable(req.IgnoreColumns), &models.AdminUser{})

	qb := squirrel.Select(sqlTool.GetQueryColumnList()).
		From(fmt.Sprintf("%v AS j", sqlTool.GetTableName())).
		PlaceholderFormat(squirrel.Dollar)

	qb = selectAdminUserQueryBuilder(qb, req)

	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	err = sqlTool.Get(AdminUser, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return AdminUser, err
}

func (c AdminUserManager) UpdateAdminUser(ctx context.Context, req *services.AdminUserRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool := mysql.NewUpdateWithColumns(ctx, c.mysql, table.GetAdminUserTable(nil), req.Model, req.UpdatedFields)
	// Build query
	qb := squirrel.Update(sqlTool.GetTableName()).
		SetMap(sqlTool.GetUpdateMap()).
		PlaceholderFormat(squirrel.Dollar)

	qb = updateAdminUserQueryBuilder(qb, req)
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	// Perform insert
	result, err := sqlTool.Update(query, args...)
	if err != nil {
		if dms_utils.IsDuplicateError(err) {
			return nil, mysql.ErrDuplicateData
		}
		if dms_utils.IsDeadlockError(err) {
			return nil, mysql.ErrDeadlock
		}
		return nil, err
	}

	rows, _ := result.RowsAffected()
	lastId, _ := result.LastInsertId()

	return &uchiha_models.CommonResponse{
		Status:       true,
		RowsAffected: rows,
		LastId:       lastId,
	}, err
}

func selectAdminUserQueryBuilder(qb squirrel.SelectBuilder, req *services.AdminUserRequest) squirrel.SelectBuilder {
	if req.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Id})
	}

	if len(req.Email) > 0 {
		qb = qb.Where(squirrel.Eq{"email": req.Email})
	}

	if len(req.ConfirmationToken) > 0 {
		qb = qb.Where(squirrel.Eq{"confirmation_token": req.ConfirmationToken})
	}

	limit := int64(250)
	if req.Limit > 0 {
		limit = req.Limit
	}

	qb = qb.Limit(uint64(limit))
	if req.Page > 0 {
		qb = qb.Offset(dms_utils.GetQueryOffset(uint64(limit), uint64(req.Page)))
	}

	return qb
}

func updateAdminUserQueryBuilder(qb squirrel.UpdateBuilder, req *services.AdminUserRequest) squirrel.UpdateBuilder {
	if req.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Id})
	}

	if len(req.Email) > 0 {
		qb = qb.Where(squirrel.Eq{"email": req.Email})
	}

	return qb
}
