package table

import (
	"barber-api/exmsg/models"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	"github.com/protrip/uchiha-core/utils/dms_utils"
)

func init() {
	uchiha_orm.RegisterTable[models.Customer](GetCustomerTable)
	uchiha_orm.RegisterTable[models.Service](GetServiceTable)
	uchiha_orm.RegisterTable[models.Booking](GetBookingTable)
	uchiha_orm.RegisterTable[models.Staff](GetStaffTable)
	uchiha_orm.RegisterTable[models.Transaction](GetTransactionTable)
	uchiha_orm.RegisterTable[models.ServiceCustom](GetServiceCustomsTable)
	uchiha_orm.RegisterTable[models.StaffOff](GetStaffOffTable)
	uchiha_orm.RegisterTable[models.TimePeriod](GetTimePeriodTable)
	uchiha_orm.RegisterTable[models.BookingInDay](GetTimePeriodTable)
}

func GetCustomerTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "customers",
		DateTimeColumns: []string{
			"updated_at", "created_at",
		},
		AIColumns:                 "id",
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "name"},
	}

	return dms_utils.GetTableWithIgnoreColumns2(table, ignoreColumns)
}

func GetAdminUserTable(ignoreColumns []string) mysql.Table {
	table := mysql.Table{
		Name: "admin_user",
		DateTimeColumns: []string{
			"updated_at", "created_at", "password_reset_at", "password_requested_at",
		},
		AIColumns:                 "id",
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "email", "salt", "password"},
	}

	return dms_utils.GetTableWithIgnoreColumns2(table, ignoreColumns)
}

// GetServiceTable returns the service table
func GetServiceTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "services",
		DateTimeColumns: []string{
			"updated_at", "created_at", "deleted_at",
		},
		AIColumns:                 "id",
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "name", "is_available"},
	}

	return dms_utils.GetTableWithIgnoreColumns(table, ignoreColumns)
}

func GetBookingTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "bookings",
		DateTimeColumns: []string{
			"updated_at", "created_at",
		},
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "staff_id", "customer_id", "date", "time_period_name", "booking_services"},
		ConvertJSONKey2Column:     nil,
		AIColumns:                 "id",
		XssIgnoreColumns:          nil,
		ColumnCustomAction:        nil,
	}

	return dms_utils.GetTableWithIgnoreColumns(table, ignoreColumns)
}

func GetStaffTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "staff",
		DateTimeColumns: []string{
			"updated_at", "created_at",
		},
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "is_available"},
		AIColumns:                 "id",
		DefaultColumns:            []string{"is_available"},
	}

	return dms_utils.GetTableWithIgnoreColumns(table, ignoreColumns)
}

func GetTransactionTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "transactions",
		DateTimeColumns: []string{
			"updated_at", "created_at",
		},
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "booking_id", "staff_id", "price"},
		AIColumns:                 "id",
	}

	return dms_utils.GetTableWithIgnoreColumns(table, ignoreColumns)
}

// GetServiceCustomsTable returns the services_custom table
func GetServiceCustomsTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "services_custom",
		DateTimeColumns: []string{
			"updated_at", "created_at",
		},
		AIColumns:                 "id",
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "service_id", "staff_id", "time_period_name", "price"},
	}

	return dms_utils.GetTableWithIgnoreColumns(table, ignoreColumns)
}

// GetStaffOffTable returns the staff_off table
func GetStaffOffTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "staff_off",
		DateTimeColumns: []string{
			"updated_at", "created_at",
		},
		AIColumns:                 "id",
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "staff_id", "date", "time_period_name"},
	}

	return dms_utils.GetTableWithIgnoreColumns2(table, ignoreColumns)
}

func GetTimePeriodTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name: "time_periods",
		DateTimeColumns: []string{
			"updated_at", "created_at",
		},
		AIColumns:                 "id",
		AutoUpdateDateTimeColumns: []string{"updated_at"},
		AutoCreateDateTimeColumns: []string{"created_at"},
		NotNullColumns:            []string{"id", "name"},
	}

	return dms_utils.GetTableWithIgnoreColumns2(table, ignoreColumns)
}
