package server

import (
	"barber-api/exmsg/models"
	"context"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
)

func (s *AdminDmsServer) GetCustomers(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Customers, error) {
	res, err := uchiha_orm.GetObjectsByOptions[models.Customer](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return &models.Customers{
		List: uchiha_orm.ToSliceProto[models.Customer](res.List),
	}, nil
}

func (s *AdminDmsServer) GetCustomer(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Customer, error) {
	res, err := uchiha_orm.GetObjectByOptions[models.Customer](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return uchiha_orm.ToProto[models.Customer](res.Model), nil
}

func (s *AdminDmsServer) InsertCustomer(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	res, err := uchiha_orm.InsertObjectByOptions[models.Customer](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return res, nil

}

func (s *AdminDmsServer) InsertCustomers(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	res, err := uchiha_orm.InsertMultipleObjectByOptions[models.Customer](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return res, nil

}
