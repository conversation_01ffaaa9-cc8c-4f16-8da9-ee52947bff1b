package server

import (
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"context"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	context2 "golang.org/x/net/context"
)

func (s *AdminDmsServer) GetAdminUserByOptions(ctx context.Context, req *services.AdminUserRequest) (*models.AdminUser, error) {
	return s.adminUserManager.GetAdminUserByOptions(ctx, req)
}

func (s *AdminDmsServer) UpdateAdminUser(ctx context2.Context, req *services.AdminUserRequest) (*uchiha_models.CommonResponse, error) {
	return s.adminUserManager.UpdateAdminUser(ctx, req)
}

func (s *AdminDmsServer) InsertAdminUser(ctx context2.Context, req *services.AdminUserRequest) (*models.AdminUser, error) {
	res, err := s.adminUserManager.InsertAdminUser(ctx, req)
	if err != nil {
		return nil, err
	}

	user, err := s.GetAdminUserByOptions(ctx, &services.AdminUserRequest{
		Id:            res.LastId,
		IgnoreColumns: req.IgnoreColumns,
	})
	if err != nil {
		return nil, err
	}
	return user, nil
}
