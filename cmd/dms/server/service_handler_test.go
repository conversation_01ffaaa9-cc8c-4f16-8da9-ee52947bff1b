package server

import (
	"barber-api/cmd/dms/table"
	"barber-api/exmsg/models"
	"context"
	"database/sql"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/bmizerany/assert"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"testing"
)

func PrepareServiceTesting(db *sql.DB) *AdminDmsServer {
	mockDb, err := mysql.NewMysqlFromRawDB(db)
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}

	uchiha_orm.RegisterTable[models.Customer](table.GetCustomerTable)
	return &AdminDmsServer{db: mockDb, portableManager: uchiha_orm.NewCoreManagerPortable(mockDb, "psql")}
}

func TestCreateServices(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}
	server := PrepareServiceTesting(db)

	for _, testCase := range []struct {
		Label       string
		SetupExpect func(sqlmock.Sqlmock)
		Request     *uchiha_models.CommonRequest
		Expect      []int64
	}{
		{
			Label: "create services",
			SetupExpect: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("INSERT INTO services").
					ExpectQuery().
					WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1).AddRow(2))
			},
			Request: &uchiha_models.CommonRequest{
				List: uchiha_orm.ToSliceStruct([]*models.Service{
					{
						Name:  "service 1",
						Price: 10000,
					},
					{
						Name:  "service 2",
						Price: 20000,
					},
				}),
			},
			Expect: []int64{1, 2},
		},
	} {
		t.Run(testCase.Label, func(t *testing.T) {
			testCase.SetupExpect(mock)
			ctx := context.Background()

			resp, err := server.CreateServices(ctx, testCase.Request)
			assert.Equal(t, err, nil)
			assert.Equal(t, testCase.Expect, resp)

		})
	}
}
