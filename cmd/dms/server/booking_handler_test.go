package server

import (
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"context"
	"fmt"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"github.com/spf13/viper"
	"testing"
)

func TestInsertBooking(t *testing.T) {
	viper.SetConfigFile("/Users/<USER>/go/src/barber-api/conf.toml")
	err := viper.ReadInConfig()
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}
	s := NewAdminDmsServer()
	a, b := s.InsertBooking(context.Background(), &uchiha_models.CommonRequest{
		Model: uchiha_orm.ToStruct(&models.Booking{
			Id:             0,
			CustomerId:     2,
			StaffId:        1,
			Date:           "2025-04-03",
			TimePeriodName: "10:30",
			Note:           "Phong rieng",
			BookingServices: []*models.BookingService{
				{
					ServiceCustomId: 1,
					ServiceId:       3,
					Price:           150000,
				},
			},
			Status: "queued",
		}),
	})
	fmt.Println(a)
	fmt.Println(b)
}

func TestAdminDmsServer_GetBookingInDay(t *testing.T) {
	viper.SetConfigFile("/Users/<USER>/go/src/barber-api/conf.toml")
	err := viper.ReadInConfig()
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}
	s := NewAdminDmsServer()
	a, b := s.GetBookingInDay(context.Background(), &services.BookingRequest{
		Date: "2025-04-10",
	})
	fmt.Println(a)
	fmt.Println(b)
}
