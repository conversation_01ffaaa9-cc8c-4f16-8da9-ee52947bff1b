package server

import (
	manager "barber-api/cmd/dms/managers"
	"context"
	"database/sql"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
)

type AdminDmsServer struct {
	db               *mysql.Mysql
	portableManager  *uchiha_orm.CoreManagerPortable
	adminUserManager *manager.AdminUserManager
}

func NewAdminDmsServer() *AdminDmsServer {
	psqlCon := mysql.DefaultPostGreConnectionFromConfig()
	db, err := mysql.NewPostgreConnection(psqlCon)
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}

	return &AdminDmsServer{
		db:               db,
		portableManager:  uchiha_orm.NewCoreManagerPortable(db, "psql"),
		adminUserManager: manager.NewAdminUserManager(db),
	}
}

// ExecuteRawQuery executes a raw SQL query and returns the rows
func (s *AdminDmsServer) ExecuteRawQuery(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return s.db.QueryContext(ctx, query, args...)
}
