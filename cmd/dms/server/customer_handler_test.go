package server

import (
	"barber-api/cmd/dms/table"
	"barber-api/exmsg/models"
	"context"
	"database/sql"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/bmizerany/assert"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"github.com/spf13/viper"
	"testing"
)

func PrepareTesting(db *sql.DB) *AdminDmsServer {
	mockDb, err := mysql.NewMysqlFromRawDB(db)
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}

	uchiha_orm.RegisterTable[models.Customer](table.GetCustomerTable)
	return &AdminDmsServer{db: mockDb, portableManager: uchiha_orm.NewCoreManagerPortable(mockDb, "psql")}
}

func TestInsertObjectByOptions(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}
	server := PrepareTesting(db)

	for _, testCase := range []struct {
		Label       string
		SetupExpect func(sqlmock.Sqlmock)
		Request     *uchiha_models.CommonRequest
		Expect      *models.Customers
	}{
		{
			Label: "get customers",
			SetupExpect: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(`SELECT id,name,phone_number FROM customers WHERE id = \$1 LIMIT 250`).
					ExpectQuery().WithArgs(1).
					WillReturnRows(sqlmock.NewRows([]string{"id", "name", "phone_number"}).
						AddRow(1, "test", "123456789"))
			},
			Request: &uchiha_models.CommonRequest{
				Filters:       nil,
				Id:            1,
				IgnoreColumns: []string{"created_at", "updated_at"},
			},
			Expect: &models.Customers{
				List: []*models.Customer{{
					Id:          1,
					Name:        "test",
					PhoneNumber: "123456789",
				}},
			},
		},
	} {
		t.Run(testCase.Label, func(t *testing.T) {
			testCase.SetupExpect(mock)
			ctx := context.Background()

			resp, err := server.GetCustomers(ctx, testCase.Request)
			assert.Equal(t, err, nil)
			assert.Equal(t, testCase.Expect, resp)

			//customer := &models.Customers{
			//	List: nil,
			//}

			//commonReq := &uchiha_models.CommonRequest{
			//	List: uchiha_orm.ToSliceStruct(customer.List),
			//	Filters:
			//}
		})
	}
}

func TestInsertCustomer(t *testing.T) {
	viper.SetConfigFile("/Users/<USER>/go/src/barber-api/conf.toml")
	err := viper.ReadInConfig()
	if err != nil {
		uchiha_log.Logger().Fatal(err)
	}
	s := NewAdminDmsServer()
	a, b := s.InsertCustomer(context.Background(), &uchiha_models.CommonRequest{
		Model: uchiha_orm.ToStruct(&models.Customer{
			Name:        "ahih",
			PhoneNumber: "adfdas",
		}),
	})
	fmt.Println(a)
	fmt.Println(b)
}

func TestInsertCustomers(t *testing.T) {
	//viper.SetConfigFile("/Users/<USER>/go/src/barber-api/conf.toml")
	//err := viper.ReadInConfig()
	//if err != nil {
	//	uchiha_log.Logger().Fatal(err)
	//}
	//s := NewAdminDmsServer()
	//a, b := s.InsertCustomers(context.Background(), &uchiha_models.CommonRequest{
	//	Filters: uchiha_orm.ToSliceStruct([]*uchiha_orm.Filter{
	//		{
	//			Field: "id",
	//			Mode:  uchiha_orm.Eq,
	//			Value: req.Ids,
	//		},
	//	}),
	//}),
	//	fmt.Println(a)
	//errors.WithStack()
	//fmt.Println(b)
}
