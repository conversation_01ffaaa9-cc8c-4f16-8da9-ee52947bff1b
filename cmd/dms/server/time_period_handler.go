package server

import (
	"barber-api/exmsg/models"
	"context"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
)

func (s *AdminDmsServer) GetTimePeriods(ctx context.Context, req *uchiha_models.CommonRequest) (*models.TimePeriods, error) {
	result, err := uchiha_orm.GetObjectsByOptions[models.TimePeriod](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return &models.TimePeriods{
		List: uchiha_orm.ToSliceProto[models.TimePeriod](result.List),
	}, nil
}
