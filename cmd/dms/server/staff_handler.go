package server

import (
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"context"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
)

func (s *AdminDmsServer) CreateStaff(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	return uchiha_orm.InsertObjectByOptions[models.Staff](ctx, s.portableManager, req)
}

func (s *AdminDmsServer) UpdateStaff(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	return uchiha_orm.UpdateObjectsByOptions[models.Staff](ctx, s.portableManager, req)
}

// GetStaffs gets list staff
func (s *AdminDmsServer) GetStaffs(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Staffs, error) {
	res, err := uchiha_orm.GetObjectsByOptions[models.Staff](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return &models.Staffs{
		List: uchiha_orm.ToSliceProto[models.Staff](res.List),
	}, nil
}

// GetStaff gets staff
func (s *AdminDmsServer) GetStaff(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Staff, error) {
	res, err := uchiha_orm.GetObjectByOptions[models.Staff](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}

	return uchiha_orm.ToProto[models.Staff](res.Model), nil
}

// DeleteStaff deletes staff
func (s *AdminDmsServer) DeleteStaff(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	_, err := uchiha_orm.DeleteObjectByOptions[models.Staff](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}

	return &uchiha_models.CommonResponse{}, nil
}

// GetStaffOffs gets staff offs
func (s *AdminDmsServer) GetStaffOffs(ctx context.Context, req *uchiha_models.CommonRequest) (*models.StaffOffs, error) {
	res, err := uchiha_orm.GetObjectsByOptions[models.StaffOff](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}

	return &models.StaffOffs{
		List: uchiha_orm.ToSliceProto[models.StaffOff](res.List),
	}, nil
}

func (s *AdminDmsServer) UpdateStaffOffInStaff(ctx context.Context, req *services.UpdateStaffOff) (*uchiha_models.CommonResponse, error) {
	sql := mysql.NewTransactions(ctx, s.db)
	rollback := func() {
		rErr := sql.Rollback()
		if rErr != nil {
			uchiha_log.Logger().Error("Can not rollback transaction", "err", rErr.Error())
		}
	}
	if len(req.Upsert) > 0 {
		_, err := uchiha_orm.InsertMultipleObjectByTrx[models.StaffOff](&sql, s.portableManager, &uchiha_models.CommonRequest{
			List:   uchiha_orm.ToSliceStruct(req.Upsert),
			Suffix: " ON CONFLICT (staff_id, date, time_period_name) DO NOTHING",
		})
		if err != nil {
			uchiha_log.Logger().Error("Can not upsert service custom", "err", err.Error())
			rollback()
			return nil, err
		}
	}

	if len(req.Delete) > 0 {
		_, err := uchiha_orm.DeleteObjectByOptionsWithTrx[models.StaffOff](&sql, s.portableManager, &uchiha_models.CommonRequest{
			Ids: req.Delete,
		})
		if err != nil {
			uchiha_log.Logger().Error("Can not delete service custom", "err", err.Error())
			rollback()
			return nil, err
		}
	}

	err := sql.Commit()
	if err != nil {
		uchiha_log.Logger().Error("Can not commit transaction", "err", err.Error())
		rollback()
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		Status: true,
	}, nil
}
