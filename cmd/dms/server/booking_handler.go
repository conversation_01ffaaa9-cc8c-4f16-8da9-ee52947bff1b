package server

import (
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"context"
	"fmt"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
)

func (s *AdminDmsServer) InsertBooking(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	result, err := uchiha_orm.InsertObjectByOptions[models.Booking](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminDmsServer) GetBookings(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Bookings, error) {
	res, err := uchiha_orm.GetObjectsByOptions[models.Booking](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return &models.Bookings{
		List: uchiha_orm.ToSliceProto[models.Booking](res.List),
	}, nil
}

func (s *AdminDmsServer) GetBooking(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Booking, error) {
	res, err := uchiha_orm.GetObjectByOptions[models.Booking](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return uchiha_orm.ToProto[models.Booking](res.Model), nil
}

func (s *AdminDmsServer) UpdateBooking(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	result, err := uchiha_orm.UpdateObjectsByOptions[models.Booking](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminDmsServer) GetBookingInDay(ctx context.Context, req *services.BookingRequest) (*models.BookingInDays, error) {
	result, err := uchiha_orm.GetObjectsByOptions[models.BookingInDay](ctx, s.portableManager, &uchiha_models.CommonRequest{
		TablePrefix: "t",
		LeftJoinTables: []*uchiha_models.JoinTable{
			{
				Name:   "bookings",
				Prefix: "b",
				On:     fmt.Sprintf("b.time_period_name = t.name and b.date = '%s'", req.Date),
			},
		},
		CustomizedSelect: "t.id, b.staff_id, t.name as time_period_name, count(b.id) as count",
		GroupBy:          "t.id, b.staff_id, t.name",
		OrderBy:          "t.id asc",
	})
	if err != nil {
		return nil, err
	}
	return &models.BookingInDays{
		List: uchiha_orm.ToSliceProto[models.BookingInDay](result.List),
	}, nil
}
