package server

import (
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"context"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
)

// CreateServices create new services
func (s *AdminDmsServer) CreateServices(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	return uchiha_orm.InsertMultipleObjectByOptions[models.Service](ctx, s.portableManager, req)
}

func (s *AdminDmsServer) GetServices(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Services, error) {
	res, err := uchiha_orm.GetObjectsByOptions[models.Service](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return &models.Services{
		List: uchiha_orm.ToSliceProto[models.Service](res.List),
	}, nil
}

// GetServiceCustoms gets service customs
func (s *AdminDmsServer) GetServiceCustoms(ctx context.Context, req *uchiha_models.CommonRequest) (*models.ServiceCustoms, error) {
	res, err := uchiha_orm.GetObjectsByOptions[models.ServiceCustom](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return &models.ServiceCustoms{
		List: uchiha_orm.ToSliceProto[models.ServiceCustom](res.List),
	}, nil
}

// GetService gets service
func (s *AdminDmsServer) GetService(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Service, error) {
	res, err := uchiha_orm.GetObjectByOptions[models.Service](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}

	return uchiha_orm.ToProto[models.Service](res.Model), nil
}

func (s *AdminDmsServer) UpdateServiceCustomInService(ctx context.Context, req *services.UpdateServiceCustom) (*uchiha_models.CommonResponse, error) {
	sql := mysql.NewTransactions(ctx, s.db)
	rollback := func() {
		rErr := sql.Rollback()
		if rErr != nil {
			uchiha_log.Logger().Error("Can not rollback transaction", "err", rErr.Error())
		}
	}
	if len(req.Upsert) > 0 {
		_, err := uchiha_orm.InsertMultipleObjectByTrx[models.ServiceCustom](&sql, s.portableManager, &uchiha_models.CommonRequest{
			List:   uchiha_orm.ToSliceStruct(req.Upsert),
			Suffix: " ON CONFLICT (service_id, staff_id, time_period_name) DO UPDATE SET price = EXCLUDED.price",
		})
		if err != nil {
			uchiha_log.Logger().Error("Can not upsert service custom", "err", err.Error())
			rollback()
			return nil, err
		}
	}

	if len(req.Delete) > 0 {
		_, err := uchiha_orm.DeleteObjectByOptionsWithTrx[models.ServiceCustom](&sql, s.portableManager, &uchiha_models.CommonRequest{
			Ids: req.Delete,
		})
		if err != nil {
			uchiha_log.Logger().Error("Can not delete service custom", "err", err.Error())
			rollback()
			return nil, err
		}
	}

	err := sql.Commit()
	if err != nil {
		uchiha_log.Logger().Error("Can not commit transaction", "err", err.Error())
		rollback()
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		Status: true,
	}, nil
}
