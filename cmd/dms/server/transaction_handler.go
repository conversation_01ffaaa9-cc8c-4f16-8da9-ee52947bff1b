package server

import (
	"barber-api/exmsg/models"
	"context"
	uchiha_orm "github.com/protrip/uchiha-core/core/orm"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
)

func (s *AdminDmsServer) InsertTransactions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	result, err := uchiha_orm.InsertMultipleObjectByOptions[models.Transaction](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminDmsServer) GetTransactions(ctx context.Context, req *uchiha_models.CommonRequest) (*models.Transactions, error) {
	res, err := uchiha_orm.GetObjectsByOptions[models.Transaction](ctx, s.portableManager, req)
	if err != nil {
		return nil, err
	}
	return &models.Transactions{
		List: uchiha_orm.ToSliceProto[models.Transaction](res.List),
	}, nil
}
