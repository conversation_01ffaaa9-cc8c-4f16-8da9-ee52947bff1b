package main

import (
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	test_handlers "github.com/protrip/uchiha-core/example/api/handlers"
	"net/http"
)

type api struct {
	name     string
	basePath string
}

func Newapi(name, basePath string) *api {
	return &api{name: name, basePath: basePath}
}

func (s *api) Name() string {
	return s.name
}

func (s *api) BasePath() string {
	return s.basePath
}

func (s *api) Routes() transhttp.Routes {
	return transhttp.Routes{
		transhttp.Route{
			Name:     "get fraud rules",
			Method:   http.MethodGet,
			BasePath: s.basePath,
			Pattern:  "/hello",
			Handler:  &test_handlers.TestHandler{},
		},
		transhttp.Route{
			Name:     "get fraud rules",
			Method:   http.MethodGet,
			BasePath: s.basePath,
			Pattern:  "/panic",
			Handler:  &test_handlers.PanicHandler{},
		},
	}
}

func (s *api) Init() error {
	return nil
}
