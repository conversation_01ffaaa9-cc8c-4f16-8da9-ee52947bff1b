package main

import (
	"context"
	web2 "github.com/protrip/uchiha-core/web"
	"github.com/urfave/cli/v2"
	"go-micro.dev/v5"
	"go-micro.dev/v5/logger"
	"go-micro.dev/v5/web"
	"sync"
	"time"
)

func main() {
	main5()

}
func main5() {
	w := web2.NewWebApi(Newapi("test-api", "/test"))
	w.StartAndServe()
}

func main3() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Millisecond*250)
	defer cancel()

	service := micro.NewService(
		micro.Name("test"),
		micro.Context(ctx),
		micro.HandleSignal(false),
		micro.Flags(
			&cli.StringFlag{
				Name: "test.timeout",
			},
			&cli.BoolFlag{
				Name: "test.v",
			},
			&cli.StringFlag{
				Name: "test.run",
			},
			&cli.StringFlag{
				Name: "test.testlogfile",
			},
		),
	)
	w := web.NewService(
		web.MicroService(service),
		web.Context(ctx),
		web.HandleSignal(false),
	)
	// s.Init()
	// w.Init()

	var wg sync.WaitGroup
	wg.Add(2)
	go func() {
		defer wg.Done()
		err := service.Run()
		if err != nil {
			logger.Logf(logger.ErrorLevel, "micro run error: %v", err)
		}
	}()
	go func() {
		defer wg.Done()
		err := w.Run()
		if err != nil {
			logger.Logf(logger.ErrorLevel, "web run error: %v", err)
		}
	}()

	wg.Wait()

}

func main1() {
	l := logger.NewLogger(logger.WithLevel(logger.TraceLevel), logger.WithCallerSkipCount(2))

	h1 := logger.NewHelper(l).WithFields(map[string]interface{}{"key1": "val1"})
	h1.Log(logger.TraceLevel, "simple log before trace_msg1")
	h1.Trace("trace_msg1")
	h1.Log(logger.TraceLevel, "simple log after trace_msg1")
	h1.Warn("warn_msg1")

	h2 := logger.NewHelper(l).WithFields(map[string]interface{}{"key2": "val2"})
	h2.Logf(logger.TraceLevel, "formatted log before trace_msg%s", "2")
	h2.Trace("trace_msg2")
	h2.Logf(logger.TraceLevel, "formatted log after trace_msg%s", "2")
	h2.Warn("warn_msg2")

	l = logger.NewLogger(logger.WithLevel(logger.TraceLevel), logger.WithCallerSkipCount(1))
	l.Fields(map[string]interface{}{"key3": "val4"}).Log(logger.InfoLevel, "test_msg")
}

func main6() {
	l := logger.NewLogger(logger.WithLevel(logger.TraceLevel), logger.WithCallerSkipCount(2)).Fields(map[string]interface{}{"requestID": "req-1"})

	ctx := logger.NewContext(context.Background(), l)

	logger.Info("info message without request ID")
	logger.Extract(ctx).Info("info message with request ID")
}
