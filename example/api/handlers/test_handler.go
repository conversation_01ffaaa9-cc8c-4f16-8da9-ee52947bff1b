package test_handlers

import (
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	"net/http"
)

type TestHandler struct{}

type Response struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
}

func (h *TestHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	response := Response{
		Success: true,
		Message: "Hello world",
	}
	transhttp.RespondJSON(w, http.StatusOK, response)
}
