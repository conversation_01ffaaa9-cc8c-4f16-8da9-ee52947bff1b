#!/bin/bash

# VM Startup Script for <PERSON><PERSON>, <PERSON>inx, and Certbot Configuration
# This script installs <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Certbot, configures UFW firewall, and sets up basic security

set -e  # Exit on error
echo "Starting VM configuration..."

# Update system packages
echo "Updating system packages..."
apt-get update && apt-get upgrade -y

# Install essential tools
echo "Installing essential tools..."
apt-get install -y apt-transport-https ca-certificates curl software-properties-common gnupg lsb-release

# Install Docker
echo "Installing Docker..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Start and enable Docker service
systemctl start docker
systemctl enable docker

# Add current user to Docker group (avoid using sudo for docker commands)
CURRENT_USER=$(who am i | awk '{print $1}')
if [ ! -z "$CURRENT_USER" ]; then
    usermod -aG docker $CURRENT_USER
    echo "Added $CURRENT_USER to the docker group"
fi

# Install Nginx
echo "Installing Nginx..."
apt-get install -y nginx

# Create a simple default Nginx configuration
echo "Configuring Nginx..."
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/html;
    index index.html index.htm;

    server_name _;

    location / {
        try_files $uri $uri/ =404;
    }
}
EOF

# Create a simple HTML file
echo "Creating default webpage..."
cat > /var/www/html/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Welcome to my Docker and Nginx server!</title>
    <style>
        body {
            width: 35em;
            margin: 0 auto;
            font-family: Tahoma, Verdana, Arial, sans-serif;
        }
    </style>
</head>
<body>
    <h1>Success! The server is running.</h1>
    <p>If you see this page, the Nginx web server is successfully installed and
    working along with Docker.</p>
</body>
</html>
EOF

# Set appropriate permissions
chown -R www-data:www-data /var/www/html
chmod -R 755 /var/www/html

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx

# Install Certbot for SSL certificates
echo "Installing Certbot for SSL certificates..."
apt-get install -y certbot python3-certbot-nginx

# Create a helper script to easily obtain certificates for domains
cat > /usr/local/bin/setup-ssl << 'EOF'
!/bin/bash

if [ $# -eq 0 ]; then
    echo "Usage: setup-ssl domain.com [www.domain.com]"
    exit 1
fi

# Create Nginx server block for the domain
cat > /etc/nginx/sites-available/$1 << CONF
server {
    listen 80;
    listen [::]:80;

    server_name $*;

    root /var/www/html/$1;
    index index.html index.htm;

    location / {
        try_files \$uri \$uri/ =404;
    }
}
CONF

# Create directory for the website
mkdir -p /var/www/html/$1
cat > /var/www/html/$1/index.html << CONF
<!DOCTYPE html>
<html>
<head>
    <title>Welcome to $1</title>
    <style>
        body {
            width: 35em;
            margin: 0 auto;
            font-family: Tahoma, Verdana, Arial, sans-serif;
        }
    </style>
</head>
<body>
    <h1>Welcome to $1!</h1>
    <p>Your website is properly configured and ready for content.</p>
</body>
</html>
CONF

# Set proper ownership and permissions
chown -R www-data:www-data /var/www/html/$1
chmod -R 755 /var/www/html/$1

# Enable the site
ln -sf /etc/nginx/sites-available/$1 /etc/nginx/sites-enabled/
nginx -t && systemctl reload nginx

# Obtain SSL certificate
certbot --nginx -d $*

echo "Setup complete for $*!"
echo "Website files are located in /var/www/html/$1"
EOF

chmod +x /usr/local/bin/setup-ssl

# Configure auto-renewal for SSL certificates
echo "Setting up automatic renewal for SSL certificates..."
echo "0 3 * * * certbot renew --quiet" | crontab -

# Configure UFW (Uncomplicated Firewall)
echo "Configuring firewall (UFW)..."
apt-get install -y ufw

# Set default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH, HTTP, and HTTPS
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp

# Enable UFW non-interactively
echo "y" | ufw enable

# Configure fail2ban to protect SSH
echo "Installing and configuring fail2ban..."
apt-get install -y fail2ban
cat > /etc/fail2ban/jail.local << 'EOF'
[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 5
bantime = 3600
EOF

# Start and enable fail2ban
systemctl start fail2ban
systemctl enable fail2ban

# Basic security measures
echo "Applying basic security measures..."

# Secure shared memory
echo "tmpfs /run/shm tmpfs defaults,noexec,nosuid 0 0" >> /etc/fstab

# Disable root SSH login
#sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
#systemctl restart sshd

# Update system automatically
#apt-get install -y unattended-upgrades
#cat > /etc/apt/apt.conf.d/20auto-upgrades << 'EOF'
#APT::Periodic::Update-Package-Lists "1";
#APT::Periodic::Unattended-Upgrade "1";
#EOF

# Configure automatic security updates
#cat > /etc/apt/apt.conf.d/50unattended-upgrades << 'EOF'
#Unattended-Upgrade::Allowed-Origins {
#    "${distro_id}:${distro_codename}-security";
#};
#Unattended-Upgrade::Package-Blacklist {
#};
#EOF

# Setup a simple backup script
#mkdir -p /root/backups
#cat > /root/backup.sh << 'EOF'
##!/bin/bash
#DATE=$(date +%Y-%m-%d)
#tar -czf /root/backups/nginx-config-$DATE.tar.gz /etc/nginx
#tar -czf /root/backups/docker-volumes-$DATE.tar.gz /var/lib/docker/volumes
#tar -czf /root/backups/letsencrypt-$DATE.tar.gz /etc/letsencrypt
#find /root/backups -type f -mtime +7 -delete
#EOF
#chmod +x /root/backup.sh

# Add to crontab to run daily
#(crontab -l 2>/dev/null; echo "0 2 * * * /root/backup.sh") | crontab -
#
#echo "VM configuration completed successfully!"
#echo "To set up SSL for your domain, run: sudo setup-ssl yourdomain.com"