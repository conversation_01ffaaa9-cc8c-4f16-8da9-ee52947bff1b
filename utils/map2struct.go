package utils

import (
    "encoding/json"
    "github.com/mitchellh/mapstructure"
)

func Map2Struct(input interface{}, result interface{}) error {
    config := &mapstructure.DecoderConfig{ TagName: "json", Result: &result, }
    decoder, err := mapstructure.NewDecoder(config)
    if err != nil {
        return err
    }

    return decoder.Decode(input)
}

func Map2StructOverJSON(input interface{}, result interface{}) error {
    bytes, err := json.Marshal(input)
    if err != nil {
        return err
    }

    return json.Unmarshal(bytes, result)
}
