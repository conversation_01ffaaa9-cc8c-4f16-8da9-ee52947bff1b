package dms_utils

import (
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	"time"
)

// Check and add default limit or maximum limit in cases limit input out of the allowed range
func GetQueryLimit(limitInput, defaultLimit, maximumLimit uint64) uint64 {
	if limitInput < 1 {
		return defaultLimit
	} else if limitInput > maximumLimit {
		return maximumLimit
	}

	return limitInput
}

// Give me a number of query limit and the page number, I give you the number of query offset
func GetQueryOffset(limit, pageInput uint64) uint64 {
	if pageInput < 1 {
		pageInput = 1
	}

	return pageInput*limit - limit
}

func GetTableWithIgnoreColumns(table mysql.Table, ignoreColumns []string) mysql.Table {
	if ignoreColumns != nil && len(ignoreColumns) > 0 {
		if table.IgnoreColumns == nil {
			table.IgnoreColumns = []string{}
		}
		for i := range ignoreColumns {
			table.IgnoreColumns = append(table.IgnoreColumns, ignoreColumns[i])
		}
	}
	return table
}

func GetTableWithIgnoreColumns2(table mysql.Table, ignoreColumns []string) mysql.Table {
	if ignoreColumns != nil && len(ignoreColumns) > 0 {
		if table.IgnoreColumns == nil {
			table.IgnoreColumns = []string{}
		}
		for i := range ignoreColumns {
			table.IgnoreColumns = append(table.IgnoreColumns, ignoreColumns[i])
		}
	}
	return table
}

func GetTimeFromUnixTimestamp(unixTimestamp int64) (res *time.Time) {
	if unixTimestamp > 0 {
		unixTime := time.Unix(unixTimestamp, 0)
		res = &unixTime
	}

	return res
}
