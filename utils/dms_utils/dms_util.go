package dms_utils

import (
	coreMysql "github.com/go-sql-driver/mysql"
)


func IsDuplicateError(err error) bool {
	me, ok := err.(*coreMysql.MySQLError)

	if ok && me.Number == 1062 {
		return true
	}

	return false
}

func IsForeignKeyConstraintFailsError(err error) bool {
	me, ok := err.(*coreMysql.MySQLError)

	if ok && me.Number == 1452 {
		return true
	}

	return false
}

func IsDeadlockError(err error) bool {
	me, ok := err.(*coreMysql.MySQLError)

	if ok && me.Number == 1213 {
		return true
	}

	return false
}
