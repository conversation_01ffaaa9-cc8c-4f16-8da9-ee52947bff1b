package utils

import "net/http"

var _ignoreHeader = []string{
	"cookie",
	"authorization",
}

func DumpHeaderMap(request *http.Request, ignoreHeaders ...string) map[string][]string {
	h := make(map[string][]string)
	for k, v := range request.Header {
		if !IsStringSliceCaseInsensitiveContains(_ignoreHeader, k) && !IsStringSliceCaseInsensitiveContains(ignoreHeaders, k) {
			h[k] = v
		}
	}

	return h
}
