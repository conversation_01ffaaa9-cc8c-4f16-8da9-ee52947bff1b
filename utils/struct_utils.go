package utils

import (
	"encoding/json"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"reflect"
	"strings"
)

func Struct2Map(data interface{}) (mapValue map[string]interface{}) {
	rv := reflect.TypeOf(data)
	mapValue = make(map[string]interface{})

	if rv.Kind() == reflect.Struct || (rv.Kind() == reflect.Ptr && rv.Elem().Kind() == reflect.Struct) {
		b, err := json.Marshal(data)
		if err != nil {
			uchiha_log.Logger().Errorf("Convert struct to map error, %v", err)
			return
		}

		err = json.Unmarshal(b, &mapValue)
		if err != nil {
			uchiha_log.Logger().Errorf("Unmarshal to map error, %v", err)
			return
		}

		return mapValue
	}

	return
}

func GetUpdatedMapByUpdatedFields(object interface{}, updatedFields []string) map[string]interface{} {
	objectMap := Struct2Map(object)
	for key := range objectMap {
		if !SliceContain(updatedFields, key) {
			delete(objectMap, key)
		}
	}
	return objectMap
}

func GetJsonFieldsOnStruct(s interface{}) (fields []string) {
	rt := reflect.TypeOf(s)

	if rt.Kind() != reflect.Struct && (rt.Kind() != reflect.Ptr || rt.Elem().Kind() != reflect.Struct) {
		uchiha_log.Logger().Errorf("Input is not a valid struct")
		return
	}

	if rt.Kind() == reflect.Ptr {
		rt = rt.Elem()
	}

	for i := 0; i < rt.NumField(); i++ {
		if len(rt.Field(i).Tag) > 0 {
			field := strings.Split(rt.Field(i).Tag.Get("json"), ",")
			if field[0] != "-" {
				fields = append(fields, field[0])
			}
		}
	}

	return
}
