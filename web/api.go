package web

import (
	"fmt"
	"github.com/protrip/uchiha-core/core/config"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"go-micro.dev/v5/logger"
	micro_web "go-micro.dev/v5/web"
	"sync"
)

type WebApp struct {
	api WebApi
	app micro_web.Service
}

func NewWebApi(api WebApi, opts ...OptionFunc) *WebApp {
	configFlags := config.InitDefaultFlags()
	opt := initOption(opts...)
	config.ParseFlag()
	config.InitConfig(configFlags)
	if opt.InitJwt {
		config.InitJWT(configFlags)
	}

	err := api.Init()
	if err != nil {
		uchiha_log.Logger().Fatal("Can not init api", "err", err.Error())
	}

	app := micro_web.NewService(
		micro_web.HandleSignal(true),
		micro_web.Address(fmt.Sprintf("%v:%v", configFlags.HTTPHost, configFlags.HTTPPort)),
		micro_web.Server(DefaultApiServer()),
		micro_web.Handler(NewHandler(api.Routes(), api.Name())),
		micro_web.BeforeStart(func() error {
			return nil
		}),
	)
	return &WebApp{api: api, app: app}
}

func initOption(options ...OptionFunc) *WebAppOptions {
	o := &WebAppOptions{}
	for _, opt := range options {
		opt(o)
	}
	return o
}

func (w *WebApp) StartAndServe() {
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		err := w.app.Run()
		if err != nil {
			logger.Logf(logger.ErrorLevel, "web run error: %v", err)
		}
	}()

	wg.Wait()

}
