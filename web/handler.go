package web

import (
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	"github.com/protrip/uchiha-core/web/middlewares"
	"github.com/urfave/negroni"
	"net/http"
	"time"
)

func NewHandler(routes transhttp.Routes, appName string, mrs ...negroni.Handler) http.Handler {
	// init middlewares
	mr := transhttp.NewRouter(routes, appName, (30 * time.Second).Milliseconds(), true)

	n := middlewares.InitAPIMiddlewares(mr)
	cors := middlewares.NewDefaultCors()
	return transhttp.RegisterHealthCheck(cors.<PERSON>ler(n), appName)
}
