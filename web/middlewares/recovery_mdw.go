package middlewares

import (
	"fmt"
	"github.com/protrip/uchiha-core/core/transport/transhttp"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/urfave/negroni"
	"net/http"
)

func NewRecoveryMdw() *RecoveryMiddleware {
	return &RecoveryMiddleware{}
}

func recoveryPanicHandlerFunc(info *negroni.PanicInformation) {
	uchiha_log.Logger().Error(fmt.Sprintf("Recovery catch panic: %v", info.RecoveredPanic))
}

type RecoveryMiddleware struct{}

type RecoverResponse struct {
	Success   bool   `json:"success"`
	ErrorCode string `json:"error_code"`
}

func (h *RecoveryMiddleware) ServeHTTP(rw http.ResponseWriter, r *http.Request, next http.HandlerFunc) {
	defer func() {
		if err := recover(); err != nil {
			//buf := make([]byte, 20000000)
			//n := runtime.Stack(buf, false)

			//uchiha_log.Logger().Error("Panic recovered",
			//	"error", err,
			//	"stack", string(buf[:n]),
			//)

			transhttp.RespondJSON(rw, http.StatusInternalServerError, RecoverResponse{
				Success:   false,
				ErrorCode: "internal_server_error",
			})
			return
		}
	}()

	next(rw, r)
}
