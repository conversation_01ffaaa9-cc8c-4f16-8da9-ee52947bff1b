package middlewares

import (
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/spf13/viper"
	"github.com/urfave/negroni"
	"net/http"
)

func InitAPIMiddlewares(handler http.Handler, mids ...negroni.Handler) *negroni.Negroni {
	n := negroni.New()

	logDisabled := viper.GetBool("api.disable_trace_log")

	if logDisabled {
		uchiha_log.Logger().Info("Disabled request & response log middleware")
	} else {
		// use logger, print each incoming request and response
		n.Use(NewNegroniBkLoggerMidFromLogger("negroni", "", ""))
	}

	// use recovery, catches panics and responds with a 500 response code
	n.Use(NewRecoveryMdw())

	// more mids
	if len(mids) > 0 {
		for _, mid := range mids {
			n.Use(mid)
		}
	}

	n.UseHandler(handler)

	return n
}
