// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto/models/common.proto

package uchiha_models // import "uchiha-core/exmsg/models"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import structpb "google.golang.org/protobuf/types/known/structpb"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CommonRequest struct {
	Filters              []*structpb.Struct `protobuf:"bytes,1,rep,name=filters" json:"filters,omitempty"`
	IgnoreColumns        []string           `protobuf:"bytes,2,rep,name=ignore_columns,json=ignoreColumns" json:"ignore_columns,omitempty"`
	UpdateColumns        []string           `protobuf:"bytes,3,rep,name=update_columns,json=updateColumns" json:"update_columns,omitempty"`
	Model                *structpb.Struct   `protobuf:"bytes,4,opt,name=model" json:"model,omitempty"`
	Limit                int64              `protobuf:"varint,5,opt,name=limit" json:"limit,omitempty"`
	Page                 int64              `protobuf:"varint,6,opt,name=page" json:"page,omitempty"`
	OrderBy              string             `protobuf:"bytes,7,opt,name=order_by,json=orderBy" json:"order_by,omitempty"`
	Id                   int64              `protobuf:"varint,8,opt,name=id" json:"id,omitempty"`
	Ids                  []int64            `protobuf:"varint,9,rep,packed,name=ids" json:"ids,omitempty"`
	MinId                int64              `protobuf:"varint,10,opt,name=min_id,json=minId" json:"min_id,omitempty"`
	MaxId                int64              `protobuf:"varint,11,opt,name=max_id,json=maxId" json:"max_id,omitempty"`
	List                 []*structpb.Struct `protobuf:"bytes,12,rep,name=list" json:"list,omitempty"`
	TablePrefix          string             `protobuf:"bytes,14,opt,name=table_prefix,json=tablePrefix" json:"table_prefix,omitempty"`
	JoinTables           []*JoinTable       `protobuf:"bytes,15,rep,name=join_tables,json=joinTables" json:"join_tables,omitempty"`
	Name                 string             `protobuf:"bytes,16,opt,name=name" json:"name,omitempty"`
	GroupBy              string             `protobuf:"bytes,17,opt,name=group_by,json=groupBy" json:"group_by,omitempty"`
	Suffix               string             `protobuf:"bytes,18,opt,name=suffix" json:"suffix,omitempty"`
	CustomizedSelect     string             `protobuf:"bytes,19,opt,name=customized_select,json=customizedSelect" json:"customized_select,omitempty"`
	LeftJoinTables       []*JoinTable       `protobuf:"bytes,20,rep,name=left_join_tables,json=leftJoinTables" json:"left_join_tables,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CommonRequest) Reset()         { *m = CommonRequest{} }
func (m *CommonRequest) String() string { return proto.CompactTextString(m) }
func (*CommonRequest) ProtoMessage()    {}
func (*CommonRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_d9be96499f0491bc, []int{0}
}
func (m *CommonRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonRequest.Unmarshal(m, b)
}
func (m *CommonRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonRequest.Marshal(b, m, deterministic)
}
func (dst *CommonRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonRequest.Merge(dst, src)
}
func (m *CommonRequest) XXX_Size() int {
	return xxx_messageInfo_CommonRequest.Size(m)
}
func (m *CommonRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CommonRequest proto.InternalMessageInfo

func (m *CommonRequest) GetFilters() []*structpb.Struct {
	if m != nil {
		return m.Filters
	}
	return nil
}

func (m *CommonRequest) GetIgnoreColumns() []string {
	if m != nil {
		return m.IgnoreColumns
	}
	return nil
}

func (m *CommonRequest) GetUpdateColumns() []string {
	if m != nil {
		return m.UpdateColumns
	}
	return nil
}

func (m *CommonRequest) GetModel() *structpb.Struct {
	if m != nil {
		return m.Model
	}
	return nil
}

func (m *CommonRequest) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *CommonRequest) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *CommonRequest) GetOrderBy() string {
	if m != nil {
		return m.OrderBy
	}
	return ""
}

func (m *CommonRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CommonRequest) GetIds() []int64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *CommonRequest) GetMinId() int64 {
	if m != nil {
		return m.MinId
	}
	return 0
}

func (m *CommonRequest) GetMaxId() int64 {
	if m != nil {
		return m.MaxId
	}
	return 0
}

func (m *CommonRequest) GetList() []*structpb.Struct {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *CommonRequest) GetTablePrefix() string {
	if m != nil {
		return m.TablePrefix
	}
	return ""
}

func (m *CommonRequest) GetJoinTables() []*JoinTable {
	if m != nil {
		return m.JoinTables
	}
	return nil
}

func (m *CommonRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CommonRequest) GetGroupBy() string {
	if m != nil {
		return m.GroupBy
	}
	return ""
}

func (m *CommonRequest) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *CommonRequest) GetCustomizedSelect() string {
	if m != nil {
		return m.CustomizedSelect
	}
	return ""
}

func (m *CommonRequest) GetLeftJoinTables() []*JoinTable {
	if m != nil {
		return m.LeftJoinTables
	}
	return nil
}

type JoinTable struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Prefix               string   `protobuf:"bytes,2,opt,name=prefix" json:"prefix,omitempty"`
	On                   string   `protobuf:"bytes,3,opt,name=on" json:"on,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinTable) Reset()         { *m = JoinTable{} }
func (m *JoinTable) String() string { return proto.CompactTextString(m) }
func (*JoinTable) ProtoMessage()    {}
func (*JoinTable) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_d9be96499f0491bc, []int{1}
}
func (m *JoinTable) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinTable.Unmarshal(m, b)
}
func (m *JoinTable) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinTable.Marshal(b, m, deterministic)
}
func (dst *JoinTable) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinTable.Merge(dst, src)
}
func (m *JoinTable) XXX_Size() int {
	return xxx_messageInfo_JoinTable.Size(m)
}
func (m *JoinTable) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinTable.DiscardUnknown(m)
}

var xxx_messageInfo_JoinTable proto.InternalMessageInfo

func (m *JoinTable) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *JoinTable) GetPrefix() string {
	if m != nil {
		return m.Prefix
	}
	return ""
}

func (m *JoinTable) GetOn() string {
	if m != nil {
		return m.On
	}
	return ""
}

type CommonResponse struct {
	List                 []*structpb.Struct `protobuf:"bytes,1,rep,name=list" json:"list,omitempty"`
	Model                *structpb.Struct   `protobuf:"bytes,2,opt,name=model" json:"model,omitempty"`
	RowsAffected         int64              `protobuf:"varint,3,opt,name=rows_affected,json=rowsAffected" json:"rows_affected,omitempty"`
	LastId               int64              `protobuf:"varint,4,opt,name=last_id,json=lastId" json:"last_id,omitempty"`
	Status               bool               `protobuf:"varint,5,opt,name=status" json:"status,omitempty"`
	LastIds              []int64            `protobuf:"varint,6,rep,packed,name=last_ids,json=lastIds" json:"last_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CommonResponse) Reset()         { *m = CommonResponse{} }
func (m *CommonResponse) String() string { return proto.CompactTextString(m) }
func (*CommonResponse) ProtoMessage()    {}
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_d9be96499f0491bc, []int{2}
}
func (m *CommonResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CommonResponse.Unmarshal(m, b)
}
func (m *CommonResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CommonResponse.Marshal(b, m, deterministic)
}
func (dst *CommonResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommonResponse.Merge(dst, src)
}
func (m *CommonResponse) XXX_Size() int {
	return xxx_messageInfo_CommonResponse.Size(m)
}
func (m *CommonResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CommonResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CommonResponse proto.InternalMessageInfo

func (m *CommonResponse) GetList() []*structpb.Struct {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *CommonResponse) GetModel() *structpb.Struct {
	if m != nil {
		return m.Model
	}
	return nil
}

func (m *CommonResponse) GetRowsAffected() int64 {
	if m != nil {
		return m.RowsAffected
	}
	return 0
}

func (m *CommonResponse) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *CommonResponse) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

func (m *CommonResponse) GetLastIds() []int64 {
	if m != nil {
		return m.LastIds
	}
	return nil
}

type CountResponse struct {
	Count                int64    `protobuf:"varint,1,opt,name=count" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountResponse) Reset()         { *m = CountResponse{} }
func (m *CountResponse) String() string { return proto.CompactTextString(m) }
func (*CountResponse) ProtoMessage()    {}
func (*CountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_common_d9be96499f0491bc, []int{3}
}
func (m *CountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountResponse.Unmarshal(m, b)
}
func (m *CountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountResponse.Marshal(b, m, deterministic)
}
func (dst *CountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountResponse.Merge(dst, src)
}
func (m *CountResponse) XXX_Size() int {
	return xxx_messageInfo_CountResponse.Size(m)
}
func (m *CountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CountResponse proto.InternalMessageInfo

func (m *CountResponse) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func init() {
	proto.RegisterType((*CommonRequest)(nil), "exmsg.models.CommonRequest")
	proto.RegisterType((*JoinTable)(nil), "exmsg.models.JoinTable")
	proto.RegisterType((*CommonResponse)(nil), "exmsg.models.CommonResponse")
	proto.RegisterType((*CountResponse)(nil), "exmsg.models.CountResponse")
}

func init() { proto.RegisterFile("proto/models/common.proto", fileDescriptor_common_d9be96499f0491bc) }

var fileDescriptor_common_d9be96499f0491bc = []byte{
	// 580 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x94, 0x41, 0x6f, 0xd3, 0x30,
	0x14, 0xc7, 0x95, 0xa6, 0x4d, 0xd7, 0xd7, 0xae, 0x74, 0x66, 0x6c, 0x1e, 0xe2, 0x50, 0x8a, 0x86,
	0x22, 0x4d, 0x4b, 0x05, 0x5c, 0x90, 0x38, 0x6d, 0x3b, 0xa0, 0x71, 0x42, 0x19, 0x27, 0x2e, 0x51,
	0x1a, 0x3b, 0x99, 0xa7, 0xc4, 0x0e, 0xb1, 0x23, 0x3a, 0xbe, 0x07, 0x1f, 0x8f, 0xef, 0x82, 0xfc,
	0x9c, 0x66, 0x3b, 0x4d, 0xbb, 0xf9, 0xff, 0x7b, 0x7f, 0xbf, 0x3c, 0xbf, 0xf7, 0x5a, 0x38, 0xa9,
	0x1b, 0x65, 0xd4, 0xba, 0x52, 0x8c, 0x97, 0x7a, 0x9d, 0xa9, 0xaa, 0x52, 0x32, 0x42, 0x46, 0x66,
	0x7c, 0x5b, 0xe9, 0x22, 0x72, 0xa1, 0xd7, 0x6f, 0x0a, 0xa5, 0x8a, 0x92, 0xaf, 0x31, 0xb6, 0x69,
	0xf3, 0xb5, 0x36, 0x4d, 0x9b, 0x19, 0xe7, 0x5d, 0xfd, 0x1d, 0xc1, 0xfe, 0x15, 0x5e, 0x8e, 0xf9,
	0xaf, 0x96, 0x6b, 0x43, 0x3e, 0xc0, 0x38, 0x17, 0xa5, 0xe1, 0x8d, 0xa6, 0xde, 0xd2, 0x0f, 0xa7,
	0x1f, 0x8f, 0x23, 0x97, 0x21, 0xda, 0x65, 0x88, 0x6e, 0x30, 0x43, 0xbc, 0xf3, 0x91, 0x53, 0x98,
	0x8b, 0x42, 0xaa, 0x86, 0x27, 0x99, 0x2a, 0xdb, 0x4a, 0x6a, 0x3a, 0x58, 0xfa, 0xe1, 0x24, 0xde,
	0x77, 0xf4, 0xca, 0x41, 0x6b, 0x6b, 0x6b, 0x96, 0x9a, 0x07, 0x9b, 0xef, 0x6c, 0x8e, 0xee, 0x6c,
	0xe7, 0x30, 0xc2, 0xd2, 0xe9, 0x70, 0xe9, 0x3d, 0xf5, 0x79, 0xe7, 0x22, 0x87, 0x30, 0x2a, 0x45,
	0x25, 0x0c, 0x1d, 0x2d, 0xbd, 0xd0, 0x8f, 0x9d, 0x20, 0x04, 0x86, 0x75, 0x5a, 0x70, 0x1a, 0x20,
	0xc4, 0x33, 0x39, 0x81, 0x3d, 0xd5, 0x30, 0xde, 0x24, 0x9b, 0x7b, 0x3a, 0x5e, 0x7a, 0xe1, 0x24,
	0x1e, 0xa3, 0xbe, 0xbc, 0x27, 0x73, 0x18, 0x08, 0x46, 0xf7, 0xd0, 0x3c, 0x10, 0x8c, 0x2c, 0xc0,
	0x17, 0x4c, 0xd3, 0xc9, 0xd2, 0x0f, 0xfd, 0xd8, 0x1e, 0xc9, 0x2b, 0x08, 0x2a, 0x21, 0x13, 0xc1,
	0x28, 0xb8, 0xef, 0x54, 0x42, 0x5e, 0x33, 0xc4, 0xe9, 0xd6, 0xe2, 0x69, 0x87, 0xd3, 0xed, 0x35,
	0x23, 0x67, 0x30, 0x2c, 0x85, 0x36, 0x74, 0xf6, 0x74, 0x07, 0xd1, 0x44, 0xde, 0xc2, 0xcc, 0xa4,
	0x9b, 0x92, 0x27, 0x75, 0xc3, 0x73, 0xb1, 0xa5, 0x73, 0xac, 0x6d, 0x8a, 0xec, 0x3b, 0x22, 0xf2,
	0x19, 0xa6, 0x77, 0x4a, 0xc8, 0x04, 0x99, 0xa6, 0x2f, 0xba, 0xb4, 0x8f, 0x07, 0x1d, 0x7d, 0x53,
	0x42, 0xfe, 0xb0, 0xf1, 0x18, 0xee, 0x76, 0x47, 0x6d, 0x1b, 0x21, 0xd3, 0x8a, 0xd3, 0x05, 0x26,
	0xc5, 0xb3, 0x6d, 0x44, 0xd1, 0xa8, 0xb6, 0xb6, 0x8d, 0x38, 0x70, 0x8d, 0x40, 0x7d, 0x79, 0x4f,
	0x8e, 0x20, 0xd0, 0x6d, 0x6e, 0xab, 0x20, 0x18, 0xe8, 0x14, 0x39, 0x83, 0x83, 0xac, 0xd5, 0x46,
	0x55, 0xe2, 0x0f, 0x67, 0x89, 0xe6, 0x25, 0xcf, 0x0c, 0x7d, 0x89, 0x96, 0xc5, 0x43, 0xe0, 0x06,
	0x39, 0xb9, 0x80, 0x45, 0xc9, 0x73, 0x93, 0x3c, 0x2e, 0xf9, 0xf0, 0xe9, 0x92, 0xe7, 0xf6, 0x42,
	0x2f, 0xf5, 0xea, 0x2b, 0x4c, 0x7a, 0xd5, 0xbf, 0xc1, 0x7b, 0xf4, 0x86, 0x23, 0x08, 0xba, 0x76,
	0x0d, 0x5c, 0xa1, 0x4e, 0xd9, 0x49, 0x2a, 0x49, 0x7d, 0x64, 0x03, 0x25, 0x57, 0xff, 0x3c, 0x98,
	0xef, 0x16, 0x5c, 0xd7, 0x4a, 0x6a, 0xde, 0x0f, 0xc7, 0x7b, 0xce, 0x70, 0xfa, 0x6d, 0x1c, 0x3c,
	0x6b, 0x1b, 0xdf, 0xc1, 0x7e, 0xa3, 0x7e, 0xeb, 0x24, 0xcd, 0x73, 0x9e, 0x19, 0xce, 0xb0, 0x12,
	0x3f, 0x9e, 0x59, 0x78, 0xd1, 0x31, 0x72, 0x0c, 0xe3, 0x32, 0xd5, 0xc6, 0x6e, 0xcd, 0x10, 0xc3,
	0x81, 0x95, 0xd7, 0x0c, 0xbb, 0x6f, 0x52, 0xd3, 0x6a, 0x5c, 0xe6, 0xbd, 0xb8, 0x53, 0x76, 0x60,
	0xdd, 0x05, 0x4d, 0x03, 0xdc, 0xc9, 0xb1, 0xbb, 0xa1, 0x57, 0xa7, 0xf6, 0xf7, 0xdb, 0x4a, 0xd3,
	0xbf, 0xee, 0x10, 0x46, 0x99, 0x05, 0xd8, 0x2d, 0x3f, 0x76, 0xe2, 0x32, 0xfc, 0xf9, 0xbe, 0xcd,
	0x6e, 0xc5, 0x6d, 0x7a, 0x9e, 0xa9, 0x86, 0xaf, 0x71, 0x0a, 0xdd, 0x9f, 0xc7, 0x17, 0x17, 0x48,
	0x9c, 0xda, 0x04, 0xf8, 0xb2, 0x4f, 0xff, 0x03, 0x00, 0x00, 0xff, 0xff, 0xa4, 0x6f, 0xd8, 0xc1,
	0x61, 0x04, 0x00, 0x00,
}
