// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto/models/admin_user.proto

package models // import "barber-api/exmsg/models"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AdminUser struct {
	Id                   int64    `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Email                string   `protobuf:"bytes,2,opt,name=email" json:"email,omitempty"`
	Password             string   `protobuf:"bytes,3,opt,name=password" json:"password,omitempty"`
	FirstName            string   `protobuf:"bytes,4,opt,name=first_name,json=firstName" json:"first_name,omitempty"`
	LastName             string   `protobuf:"bytes,5,opt,name=last_name,json=lastName" json:"last_name,omitempty"`
	Salt                 string   `protobuf:"bytes,11,opt,name=salt" json:"salt,omitempty"`
	CreatedAt            int64    `protobuf:"varint,12,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt            int64    `protobuf:"varint,13,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
	ConfirmationToken    string   `protobuf:"bytes,14,opt,name=confirmation_token,json=confirmationToken" json:"confirmation_token,omitempty"`
	PasswordResetAt      int64    `protobuf:"varint,16,opt,name=password_reset_at,json=passwordResetAt" json:"password_reset_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdminUser) Reset()         { *m = AdminUser{} }
func (m *AdminUser) String() string { return proto.CompactTextString(m) }
func (*AdminUser) ProtoMessage()    {}
func (*AdminUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_admin_user_be755d190a91759f, []int{0}
}
func (m *AdminUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminUser.Unmarshal(m, b)
}
func (m *AdminUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminUser.Marshal(b, m, deterministic)
}
func (dst *AdminUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminUser.Merge(dst, src)
}
func (m *AdminUser) XXX_Size() int {
	return xxx_messageInfo_AdminUser.Size(m)
}
func (m *AdminUser) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminUser.DiscardUnknown(m)
}

var xxx_messageInfo_AdminUser proto.InternalMessageInfo

func (m *AdminUser) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AdminUser) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *AdminUser) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

func (m *AdminUser) GetFirstName() string {
	if m != nil {
		return m.FirstName
	}
	return ""
}

func (m *AdminUser) GetLastName() string {
	if m != nil {
		return m.LastName
	}
	return ""
}

func (m *AdminUser) GetSalt() string {
	if m != nil {
		return m.Salt
	}
	return ""
}

func (m *AdminUser) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *AdminUser) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *AdminUser) GetConfirmationToken() string {
	if m != nil {
		return m.ConfirmationToken
	}
	return ""
}

func (m *AdminUser) GetPasswordResetAt() int64 {
	if m != nil {
		return m.PasswordResetAt
	}
	return 0
}

func init() {
	proto.RegisterType((*AdminUser)(nil), "exmsg.models.AdminUser")
}

func init() {
	proto.RegisterFile("proto/models/admin_user.proto", fileDescriptor_admin_user_be755d190a91759f)
}

var fileDescriptor_admin_user_be755d190a91759f = []byte{
	// 275 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0x90, 0x31, 0x4f, 0xf3, 0x30,
	0x10, 0x86, 0x95, 0xb4, 0xfd, 0xf4, 0xe5, 0x28, 0x85, 0x5a, 0x0c, 0x16, 0xa8, 0x28, 0x62, 0xaa,
	0x90, 0xda, 0x0c, 0x8c, 0x4c, 0xe1, 0x07, 0x30, 0x44, 0xb0, 0xb0, 0x44, 0x97, 0xfa, 0x8a, 0x2c,
	0xe2, 0x38, 0xb2, 0x5d, 0xc1, 0x2f, 0xe3, 0xf7, 0x21, 0x5f, 0x12, 0xc4, 0x64, 0xdf, 0xf3, 0xd8,
	0xaf, 0xec, 0x17, 0x36, 0xbd, 0xb3, 0xc1, 0x16, 0xc6, 0x2a, 0x6a, 0x7d, 0x81, 0xca, 0xe8, 0xae,
	0x3e, 0x79, 0x72, 0x7b, 0xe6, 0x62, 0x49, 0x5f, 0xc6, 0xbf, 0xef, 0x07, 0x7d, 0xf7, 0x9d, 0x42,
	0x56, 0xc6, 0x23, 0xaf, 0x9e, 0x9c, 0x58, 0x41, 0xaa, 0x95, 0x4c, 0xf2, 0x64, 0x3b, 0xab, 0x52,
	0xad, 0xc4, 0x15, 0x2c, 0xc8, 0xa0, 0x6e, 0x65, 0x9a, 0x27, 0xdb, 0xac, 0x1a, 0x06, 0x71, 0x0d,
	0xff, 0x7b, 0xf4, 0xfe, 0xd3, 0x3a, 0x25, 0x67, 0x2c, 0x7e, 0x67, 0xb1, 0x01, 0x38, 0x6a, 0xe7,
	0x43, 0xdd, 0xa1, 0x21, 0x39, 0x67, 0x9b, 0x31, 0x79, 0x46, 0x43, 0xe2, 0x06, 0xb2, 0x16, 0x27,
	0xbb, 0x18, 0xee, 0x46, 0xc0, 0x52, 0xc0, 0xdc, 0x63, 0x1b, 0xe4, 0x19, 0x73, 0xde, 0xc7, 0xbc,
	0x83, 0x23, 0x0c, 0xa4, 0x6a, 0x0c, 0x72, 0xc9, 0x2f, 0xcb, 0x46, 0x52, 0xb2, 0x3e, 0xf5, 0x6a,
	0xd2, 0xe7, 0x83, 0x1e, 0x49, 0x19, 0xc4, 0x0e, 0xc4, 0xc1, 0x76, 0x47, 0xed, 0x0c, 0x06, 0x6d,
	0xbb, 0x3a, 0xd8, 0x0f, 0xea, 0xe4, 0x8a, 0xf3, 0xd7, 0x7f, 0xcd, 0x4b, 0x14, 0xe2, 0x1e, 0xd6,
	0xd3, 0x47, 0x6a, 0x47, 0x9e, 0x42, 0x0c, 0xbd, 0xe4, 0xd0, 0x8b, 0x49, 0x54, 0x91, 0x97, 0xe1,
	0x29, 0x7f, 0xbb, 0x6d, 0xd0, 0x35, 0xe4, 0x76, 0xd8, 0xeb, 0x82, 0x3b, 0x1d, 0x2b, 0x7f, 0x1c,
	0x96, 0xe6, 0x1f, 0xf7, 0xfd, 0xf0, 0x13, 0x00, 0x00, 0xff, 0xff, 0x16, 0x7b, 0xd4, 0x2b, 0x90,
	0x01, 0x00, 0x00,
}
