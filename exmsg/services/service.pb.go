// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto/services/service.proto

package services // import "barber-api/exmsg/services"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import models "barber-api/exmsg/models"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UpdateServiceCustom struct {
	Upsert               []*models.ServiceCustom `protobuf:"bytes,1,rep,name=upsert" json:"upsert,omitempty"`
	Delete               []int64                 `protobuf:"varint,2,rep,packed,name=delete" json:"delete,omitempty"`
	UpdateFields         []string                `protobuf:"bytes,3,rep,name=updateFields" json:"updateFields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdateServiceCustom) Reset()         { *m = UpdateServiceCustom{} }
func (m *UpdateServiceCustom) String() string { return proto.CompactTextString(m) }
func (*UpdateServiceCustom) ProtoMessage()    {}
func (*UpdateServiceCustom) Descriptor() ([]byte, []int) {
	return fileDescriptor_service_687c4c50ae43be9b, []int{0}
}
func (m *UpdateServiceCustom) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateServiceCustom.Unmarshal(m, b)
}
func (m *UpdateServiceCustom) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateServiceCustom.Marshal(b, m, deterministic)
}
func (dst *UpdateServiceCustom) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateServiceCustom.Merge(dst, src)
}
func (m *UpdateServiceCustom) XXX_Size() int {
	return xxx_messageInfo_UpdateServiceCustom.Size(m)
}
func (m *UpdateServiceCustom) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateServiceCustom.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateServiceCustom proto.InternalMessageInfo

func (m *UpdateServiceCustom) GetUpsert() []*models.ServiceCustom {
	if m != nil {
		return m.Upsert
	}
	return nil
}

func (m *UpdateServiceCustom) GetDelete() []int64 {
	if m != nil {
		return m.Delete
	}
	return nil
}

func (m *UpdateServiceCustom) GetUpdateFields() []string {
	if m != nil {
		return m.UpdateFields
	}
	return nil
}

func init() {
	proto.RegisterType((*UpdateServiceCustom)(nil), "exmsg.models.UpdateServiceCustom")
}

func init() {
	proto.RegisterFile("proto/services/service.proto", fileDescriptor_service_687c4c50ae43be9b)
}

var fileDescriptor_service_687c4c50ae43be9b = []byte{
	// 184 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x29, 0x28, 0xca, 0x2f,
	0xc9, 0xd7, 0x2f, 0x4e, 0x2d, 0x2a, 0xcb, 0x4c, 0x4e, 0x2d, 0x86, 0x31, 0xf4, 0xc0, 0xc2, 0x42,
	0x3c, 0xa9, 0x15, 0xb9, 0xc5, 0xe9, 0x7a, 0xb9, 0xf9, 0x29, 0xa9, 0x39, 0xc5, 0x52, 0x92, 0x10,
	0xb5, 0x10, 0x1e, 0x94, 0x82, 0x28, 0x54, 0x6a, 0x63, 0xe4, 0x12, 0x0e, 0x2d, 0x48, 0x49, 0x2c,
	0x49, 0x0d, 0x86, 0x18, 0xe0, 0x5c, 0x5a, 0x5c, 0x92, 0x9f, 0x2b, 0x64, 0xcc, 0xc5, 0x56, 0x5a,
	0x50, 0x9c, 0x5a, 0x54, 0x22, 0xc1, 0xa8, 0xc0, 0xac, 0xc1, 0x6d, 0x24, 0xad, 0x87, 0x6c, 0xa2,
	0x1e, 0x8a, 0xe2, 0x20, 0xa8, 0x52, 0x21, 0x31, 0x2e, 0xb6, 0x94, 0xd4, 0x9c, 0xd4, 0x92, 0x54,
	0x09, 0x26, 0x05, 0x66, 0x0d, 0xe6, 0x20, 0x28, 0x4f, 0x48, 0x89, 0x8b, 0xa7, 0x14, 0x6c, 0x87,
	0x5b, 0x66, 0x6a, 0x4e, 0x4a, 0xb1, 0x04, 0xb3, 0x02, 0xb3, 0x06, 0x67, 0x10, 0x8a, 0x98, 0x93,
	0x4a, 0x94, 0x52, 0x52, 0x62, 0x51, 0x52, 0x6a, 0x91, 0x6e, 0x62, 0x41, 0xa6, 0x3e, 0xd8, 0x32,
	0xb8, 0xe7, 0xac, 0x61, 0x8c, 0x24, 0x36, 0xb0, 0xab, 0x8d, 0x01, 0x01, 0x00, 0x00, 0xff, 0xff,
	0xb6, 0xdd, 0x63, 0xfc, 0xfe, 0x00, 0x00, 0x00,
}
