// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto/services/admin-user-dms.proto

package services // import "barber-api/exmsg/services"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import models "barber-api/exmsg/models"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AdminUserRequest struct {
	Id                   int64             `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Email                string            `protobuf:"bytes,2,opt,name=email" json:"email,omitempty"`
	IgnoreColumns        []string          `protobuf:"bytes,3,rep,name=ignore_columns,json=ignoreColumns" json:"ignore_columns,omitempty"`
	Model                *models.AdminUser `protobuf:"bytes,4,opt,name=model" json:"model,omitempty"`
	Limit                int64             `protobuf:"varint,5,opt,name=limit" json:"limit,omitempty"`
	Page                 int64             `protobuf:"varint,6,opt,name=page" json:"page,omitempty"`
	GetModel_            bool              `protobuf:"varint,7,opt,name=get_model,json=getModel" json:"get_model,omitempty"`
	UpdatedFields        []string          `protobuf:"bytes,8,rep,name=updated_fields,json=updatedFields" json:"updated_fields,omitempty"`
	ConfirmationToken    string            `protobuf:"bytes,9,opt,name=confirmation_token,json=confirmationToken" json:"confirmation_token,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AdminUserRequest) Reset()         { *m = AdminUserRequest{} }
func (m *AdminUserRequest) String() string { return proto.CompactTextString(m) }
func (*AdminUserRequest) ProtoMessage()    {}
func (*AdminUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_admin_user_dms_ca4f76114858f898, []int{0}
}
func (m *AdminUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdminUserRequest.Unmarshal(m, b)
}
func (m *AdminUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdminUserRequest.Marshal(b, m, deterministic)
}
func (dst *AdminUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdminUserRequest.Merge(dst, src)
}
func (m *AdminUserRequest) XXX_Size() int {
	return xxx_messageInfo_AdminUserRequest.Size(m)
}
func (m *AdminUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AdminUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AdminUserRequest proto.InternalMessageInfo

func (m *AdminUserRequest) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AdminUserRequest) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

func (m *AdminUserRequest) GetIgnoreColumns() []string {
	if m != nil {
		return m.IgnoreColumns
	}
	return nil
}

func (m *AdminUserRequest) GetModel() *models.AdminUser {
	if m != nil {
		return m.Model
	}
	return nil
}

func (m *AdminUserRequest) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *AdminUserRequest) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *AdminUserRequest) GetGetModel_() bool {
	if m != nil {
		return m.GetModel_
	}
	return false
}

func (m *AdminUserRequest) GetUpdatedFields() []string {
	if m != nil {
		return m.UpdatedFields
	}
	return nil
}

func (m *AdminUserRequest) GetConfirmationToken() string {
	if m != nil {
		return m.ConfirmationToken
	}
	return ""
}

func init() {
	proto.RegisterType((*AdminUserRequest)(nil), "exmsg.models.AdminUserRequest")
}

func init() {
	proto.RegisterFile("proto/services/admin-user-dms.proto", fileDescriptor_admin_user_dms_ca4f76114858f898)
}

var fileDescriptor_admin_user_dms_ca4f76114858f898 = []byte{
	// 303 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0x91, 0x4d, 0x4b, 0xc4, 0x30,
	0x10, 0x86, 0x69, 0xf7, 0xc3, 0x6d, 0xd4, 0x45, 0x83, 0x60, 0x50, 0x84, 0xb2, 0xba, 0xd0, 0x4b,
	0xbb, 0xa0, 0x47, 0x4f, 0x2a, 0x78, 0xf3, 0x52, 0xf4, 0xe2, 0xa5, 0xa4, 0xcd, 0x6c, 0x19, 0x6c,
	0x92, 0x9a, 0xa4, 0xe2, 0x7f, 0xf2, 0x4f, 0x4a, 0x13, 0x3f, 0xf6, 0x36, 0xf3, 0xcc, 0xbc, 0xf3,
	0xce, 0x24, 0xe4, 0xb2, 0x37, 0xda, 0xe9, 0x8d, 0x05, 0xf3, 0x81, 0x0d, 0xd8, 0x0d, 0x17, 0x12,
	0x55, 0x3e, 0x58, 0x30, 0xb9, 0x90, 0xb6, 0xf0, 0x55, 0x7a, 0x00, 0x9f, 0xd2, 0xb6, 0x85, 0xd4,
	0x02, 0x3a, 0x7b, 0x76, 0x11, 0x24, 0x21, 0x0b, 0x82, 0x6a, 0x14, 0x84, 0xe6, 0xd5, 0x57, 0x4c,
	0x8e, 0xee, 0x46, 0xf8, 0x62, 0xc1, 0x94, 0xf0, 0x3e, 0x80, 0x75, 0x74, 0x49, 0x62, 0x14, 0x2c,
	0x4a, 0xa3, 0x6c, 0x52, 0xc6, 0x28, 0xe8, 0x09, 0x99, 0x81, 0xe4, 0xd8, 0xb1, 0x38, 0x8d, 0xb2,
	0xa4, 0x0c, 0x09, 0x5d, 0x93, 0x25, 0xb6, 0x4a, 0x1b, 0xa8, 0x1a, 0xdd, 0x0d, 0x52, 0x59, 0x36,
	0x49, 0x27, 0x59, 0x52, 0x1e, 0x06, 0xfa, 0x10, 0x20, 0xcd, 0xc9, 0xcc, 0x9b, 0xb3, 0x69, 0x1a,
	0x65, 0xfb, 0xd7, 0xa7, 0xc5, 0xee, 0x7a, 0xc5, 0xbf, 0x77, 0xe8, 0x1a, 0xbd, 0x3a, 0x94, 0xe8,
	0xd8, 0xcc, 0xdb, 0x87, 0x84, 0x52, 0x32, 0xed, 0x79, 0x0b, 0x6c, 0xee, 0xa1, 0x8f, 0xe9, 0x39,
	0x49, 0x5a, 0x70, 0x55, 0x18, 0xbe, 0x97, 0x46, 0xd9, 0xa2, 0x5c, 0xb4, 0xe0, 0x9e, 0xfc, 0x98,
	0x35, 0x59, 0x0e, 0xbd, 0xe0, 0x0e, 0x44, 0xb5, 0x45, 0xe8, 0x84, 0x65, 0x8b, 0xb0, 0xdc, 0x0f,
	0x7d, 0xf4, 0x90, 0xe6, 0x84, 0x36, 0x5a, 0x6d, 0xd1, 0x48, 0xee, 0x50, 0xab, 0xca, 0xe9, 0x37,
	0x50, 0x2c, 0xf1, 0x67, 0x1e, 0xef, 0x56, 0x9e, 0xc7, 0xc2, 0xfd, 0xd5, 0xeb, 0xaa, 0xe6, 0xa6,
	0x06, 0x93, 0xf3, 0x1e, 0x37, 0xfe, 0x90, 0xbf, 0xcf, 0xb8, 0xfd, 0x0d, 0xea, 0xb9, 0x7f, 0xda,
	0x9b, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x82, 0x88, 0x8a, 0xca, 0xae, 0x01, 0x00, 0x00,
}
