// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto/services/staff.proto

package services // import "barber-api/exmsg/services"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import models "barber-api/exmsg/models"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UpdateStaffOff struct {
	Upsert               []*models.StaffOff `protobuf:"bytes,1,rep,name=upsert" json:"upsert,omitempty"`
	Delete               []int64            `protobuf:"varint,2,rep,packed,name=delete" json:"delete,omitempty"`
	UpdateFields         []string           `protobuf:"bytes,3,rep,name=updateFields" json:"updateFields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateStaffOff) Reset()         { *m = UpdateStaffOff{} }
func (m *UpdateStaffOff) String() string { return proto.CompactTextString(m) }
func (*UpdateStaffOff) ProtoMessage()    {}
func (*UpdateStaffOff) Descriptor() ([]byte, []int) {
	return fileDescriptor_staff_1b1dc0570da18afc, []int{0}
}
func (m *UpdateStaffOff) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStaffOff.Unmarshal(m, b)
}
func (m *UpdateStaffOff) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStaffOff.Marshal(b, m, deterministic)
}
func (dst *UpdateStaffOff) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStaffOff.Merge(dst, src)
}
func (m *UpdateStaffOff) XXX_Size() int {
	return xxx_messageInfo_UpdateStaffOff.Size(m)
}
func (m *UpdateStaffOff) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStaffOff.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStaffOff proto.InternalMessageInfo

func (m *UpdateStaffOff) GetUpsert() []*models.StaffOff {
	if m != nil {
		return m.Upsert
	}
	return nil
}

func (m *UpdateStaffOff) GetDelete() []int64 {
	if m != nil {
		return m.Delete
	}
	return nil
}

func (m *UpdateStaffOff) GetUpdateFields() []string {
	if m != nil {
		return m.UpdateFields
	}
	return nil
}

func init() {
	proto.RegisterType((*UpdateStaffOff)(nil), "exmsg.models.UpdateStaffOff")
}

func init() { proto.RegisterFile("proto/services/staff.proto", fileDescriptor_staff_1b1dc0570da18afc) }

var fileDescriptor_staff_1b1dc0570da18afc = []byte{
	// 184 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x2a, 0x28, 0xca, 0x2f,
	0xc9, 0xd7, 0x2f, 0x4e, 0x2d, 0x2a, 0xcb, 0x4c, 0x4e, 0x2d, 0xd6, 0x2f, 0x2e, 0x49, 0x4c, 0x4b,
	0xd3, 0x03, 0x0b, 0x0a, 0xf1, 0xa4, 0x56, 0xe4, 0x16, 0xa7, 0xeb, 0xe5, 0xe6, 0xa7, 0xa4, 0xe6,
	0x14, 0x4b, 0x49, 0x42, 0x54, 0x42, 0x78, 0x50, 0x0a, 0xa2, 0x50, 0xa9, 0x86, 0x8b, 0x2f, 0xb4,
	0x20, 0x25, 0xb1, 0x24, 0x35, 0x18, 0xa4, 0xdb, 0x3f, 0x2d, 0x4d, 0x48, 0x8f, 0x8b, 0xad, 0xb4,
	0xa0, 0x38, 0xb5, 0xa8, 0x44, 0x82, 0x51, 0x81, 0x59, 0x83, 0xdb, 0x48, 0x4c, 0x0f, 0xd9, 0x2c,
	0x3d, 0x98, 0xba, 0x20, 0xa8, 0x2a, 0x21, 0x31, 0x2e, 0xb6, 0x94, 0xd4, 0x9c, 0xd4, 0x92, 0x54,
	0x09, 0x26, 0x05, 0x66, 0x0d, 0xe6, 0x20, 0x28, 0x4f, 0x48, 0x89, 0x8b, 0xa7, 0x14, 0x6c, 0xb2,
	0x5b, 0x66, 0x6a, 0x4e, 0x4a, 0xb1, 0x04, 0xb3, 0x02, 0xb3, 0x06, 0x67, 0x10, 0x8a, 0x98, 0x93,
	0x4a, 0x94, 0x52, 0x52, 0x62, 0x51, 0x52, 0x6a, 0x91, 0x6e, 0x62, 0x41, 0xa6, 0x3e, 0xd8, 0x1e,
	0xb8, 0x7f, 0xac, 0x61, 0x8c, 0x24, 0x36, 0xb0, 0x53, 0x8d, 0x01, 0x01, 0x00, 0x00, 0xff, 0xff,
	0x59, 0x23, 0x41, 0x3b, 0xf1, 0x00, 0x00, 0x00,
}
