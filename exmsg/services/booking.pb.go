// Code generated by protoc-gen-go. DO NOT EDIT.
// source: proto/services/booking.proto

package services // import "barber-api/exmsg/services"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BookingRequest struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BookingRequest) Reset()         { *m = BookingRequest{} }
func (m *BookingRequest) String() string { return proto.CompactTextString(m) }
func (*BookingRequest) ProtoMessage()    {}
func (*BookingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_booking_d8b3b19f62d5e233, []int{0}
}
func (m *BookingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BookingRequest.Unmarshal(m, b)
}
func (m *BookingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BookingRequest.Marshal(b, m, deterministic)
}
func (dst *BookingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BookingRequest.Merge(dst, src)
}
func (m *BookingRequest) XXX_Size() int {
	return xxx_messageInfo_BookingRequest.Size(m)
}
func (m *BookingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BookingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BookingRequest proto.InternalMessageInfo

func (m *BookingRequest) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func init() {
	proto.RegisterType((*BookingRequest)(nil), "exmsg.models.BookingRequest")
}

func init() {
	proto.RegisterFile("proto/services/booking.proto", fileDescriptor_booking_d8b3b19f62d5e233)
}

var fileDescriptor_booking_d8b3b19f62d5e233 = []byte{
	// 123 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x29, 0x28, 0xca, 0x2f,
	0xc9, 0xd7, 0x2f, 0x4e, 0x2d, 0x2a, 0xcb, 0x4c, 0x4e, 0x2d, 0xd6, 0x4f, 0xca, 0xcf, 0xcf, 0xce,
	0xcc, 0x4b, 0xd7, 0x03, 0x0b, 0x0b, 0xf1, 0xa4, 0x56, 0xe4, 0x16, 0xa7, 0xeb, 0xe5, 0xe6, 0xa7,
	0xa4, 0xe6, 0x14, 0x2b, 0xa9, 0x70, 0xf1, 0x39, 0x41, 0xa4, 0x83, 0x52, 0x0b, 0x4b, 0x53, 0x8b,
	0x4b, 0x84, 0x84, 0xb8, 0x58, 0x52, 0x12, 0x4b, 0x52, 0x25, 0x18, 0x15, 0x18, 0x35, 0x38, 0x83,
	0xc0, 0x6c, 0x27, 0x95, 0x28, 0xa5, 0xa4, 0xc4, 0xa2, 0xa4, 0xd4, 0x22, 0xdd, 0xc4, 0x82, 0x4c,
	0x7d, 0xb0, 0x01, 0x70, 0xe3, 0xad, 0x61, 0x8c, 0x24, 0x36, 0xb0, 0x05, 0xc6, 0x80, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xa9, 0x58, 0x99, 0xad, 0x80, 0x00, 0x00, 0x00,
}
