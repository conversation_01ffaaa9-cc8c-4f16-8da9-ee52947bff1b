package analytics

// GetAnalyticsRequest represents a request to get analytics data
type GetAnalyticsRequest struct {
	StylistIds []int64 `json:"stylist_ids"`
	From       string  `json:"from"`        // YYYY/MM/DD format
	To         string  `json:"to"`          // YYYY/MM/DD format
	Mode       string  `json:"mode"`        // day, month, year
}

// GetAnalyticsResponse represents the response from analytics query
type GetAnalyticsResponse struct {
	Data map[string]map[string]float64 `json:"data"`
}
