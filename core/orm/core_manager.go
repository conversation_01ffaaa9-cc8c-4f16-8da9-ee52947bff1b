package uchiha_orm

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"github.com/protrip/uchiha-core/utils"
	"github.com/protrip/uchiha-core/utils/dms_utils"
)

type CoreManager[T any] struct {
	Mysql       *mysql.Mysql
	protoObject T
	table       func(ignoreColumns ...string) mysql.Table
	dbType      string
}

func NewCoreManager[T any](Mysql *mysql.Mysql,
	dbType string,
	protoObject T,
	table func(ignoreColumns ...string) mysql.Table) *CoreManager[T] {
	return &CoreManager[T]{
		Mysql:       Mysql,
		protoObject: protoObject,
		table:       table,
		dbType:      dbType,
	}
}

func (c *CoreManager[T]) InsertObjectByOptions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool := mysql.NewInsert(ctx, c.Mysql, c.table(), ToProto[T](req.Model))

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList()).
		Values(sqlTool.GetFilledValues()...)

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	qb = qb.Suffix(req.Suffix)

	query, args, err := qb.ToSql()

	if err != nil {
		return nil, err
	}

	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQL(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.Insert(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}

	return response, nil
}

func (c *CoreManager[T]) InsertMultipleObjectByOptions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool := mysql.NewInsert(ctx, c.Mysql, c.table(), &c.protoObject)

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList())

	for _, s := range req.List {
		qb = qb.Values(sqlTool.GetFillValues(s.AsMap()))
	}

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}

	qb = qb.Suffix(req.Suffix)
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQL(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.Insert(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}

	return response, nil
}

func (c *CoreManager[T]) GetObjectsByOptions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	if len(req.JoinTables) > 0 {
		return c.GetObjectsWithJoinByOptions(ctx, req)
	}

	sqlTool := mysql.NewSelect(ctx, c.Mysql, c.table(req.IgnoreColumns...), &c.protoObject)

	qb := squirrel.Select(sqlTool.GetQueryColumnList()).
		From(sqlTool.GetTableName())

	qb = c.commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()

	if err != nil {
		return nil, err
	}

	list := make([]*T, 0)
	err = sqlTool.Select(&list, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		List: ToSliceStruct(list),
	}, nil
}

func (c *CoreManager[T]) GetObjectByOptions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool := mysql.NewSelect(ctx, c.Mysql, c.table(req.IgnoreColumns...), &c.protoObject)

	qb := squirrel.Select(sqlTool.GetQueryColumnList()).
		From(sqlTool.GetTableName())

	qb = c.commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	var s = new(T)
	err = sqlTool.Get(s, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		Model: ToStruct(s),
	}, nil
}

func (c *CoreManager[T]) GetObjectsWithJoinByOptions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool := mysql.NewSelect(ctx, c.Mysql, c.table(req.IgnoreColumns...), &c.protoObject)

	qb := squirrel.Select(sqlTool.GetQueryColumnListWithPrefix(req.TablePrefix))
	if len(req.CustomizedSelect) > 0 {
		qb = squirrel.Select(req.CustomizedSelect)
	}
	qb = qb.From(fmt.Sprintf("%v as %v", sqlTool.GetTableName(), req.TablePrefix))

	for _, tbj := range req.JoinTables {
		qb = qb.Join(fmt.Sprintf("%v as %v on %v", tbj.Name, tbj.Prefix, tbj.On))
	}

	qb = c.commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()

	if err != nil {
		return nil, err
	}

	list := make([]*T, 0)
	err = sqlTool.Select(&list, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		List: ToSliceStruct(list),
	}, nil
}

func (c *CoreManager[T]) UpdateObjectsByOptions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := ToProto[T](req.Model)
	sqlTool := mysql.NewUpdateWithColumns(ctx, c.Mysql, c.table(req.IgnoreColumns...), protoObject, req.UpdateColumns)

	qb := squirrel.Update(sqlTool.GetTableName()).
		SetMap(sqlTool.GetUpdateMap())

	qb = c.commonUpdateFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	res, err := sqlTool.Update(query, args...)
	if err != nil {
		return nil, err
	}

	affected, _ := res.RowsAffected()
	return &uchiha_models.CommonResponse{
		RowsAffected: affected,
	}, nil
}

func (c *CoreManager[T]) DeleteObjectByOptions(ctx context.Context, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool := mysql.NewDelete(ctx, c.Mysql, c.table(), &c.protoObject)

	qb := squirrel.Delete(sqlTool.GetTableName())

	qb = c.commonDeleteFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := sqlTool.Delete(query, args...)
	if err != nil {
		if dms_utils.IsDuplicateError(err) {
			return nil, mysql.ErrDuplicateData
		}
		if dms_utils.IsDeadlockError(err) {
			return nil, mysql.ErrDeadlock
		}
		return nil, err
	}

	rows, _ := result.RowsAffected()
	return &uchiha_models.CommonResponse{
		RowsAffected: rows,
	}, nil
}

func (c *CoreManager[T]) commonSelectFilter(qb squirrel.SelectBuilder, req *uchiha_models.CommonRequest) squirrel.SelectBuilder {
	filters := req.Filters

	if req.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Id})
	} else if len(req.Ids) > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Ids})
	}

	if req.MinId > 0 {
		qb = qb.Where(squirrel.Gt{"id": req.MinId})
	}

	if req.MaxId > 0 {
		qb = qb.Where(squirrel.LtOrEq{"id": req.MaxId})
	}

	if len(req.Name) > 0 {
		qb = qb.Where(squirrel.Eq{"name": req.Name})
	}

	if len(filters) > 0 {
		for _, req := range filters {
			filter := &Filter{}
			utils.Map2Struct(req.AsMap(), filter)
			switch filter.Mode {
			case Lt:
				qb = qb.Where(squirrel.Lt{filter.Field: filter.Value})
			case Lte:
				qb = qb.Where(squirrel.LtOrEq{filter.Field: filter.Value})
			case Gt:
				qb = qb.Where(squirrel.Gt{filter.Field: filter.Value})
			case Gte:
				qb = qb.Where(squirrel.GtOrEq{filter.Field: filter.Value})
			default:
				// eq
				qb = qb.Where(squirrel.Eq{filter.Field: filter.Value})
			}
		}
	}

	if len(req.OrderBy) > 0 {
		qb = qb.OrderBy(req.OrderBy)
	}

	limit := int64(250)
	if req.Limit > 0 {
		limit = req.Limit
	}

	qb = qb.Limit(uint64(limit))
	if req.Page > 0 {
		qb = qb.Offset(dms_utils.GetQueryOffset(uint64(limit), uint64(req.Page)))
	}

	return qb
}

func (c *CoreManager[T]) commonUpdateFilter(qb squirrel.UpdateBuilder, req *uchiha_models.CommonRequest) squirrel.UpdateBuilder {
	filters := req.Filters

	if len(filters) > 0 {
		for _, req := range filters {
			filter := &Filter{}
			utils.Map2Struct(req.AsMap(), filter)
			switch filter.Mode {
			case Lt:
				qb = qb.Where(squirrel.Lt{filter.Field: filter.Value})
			case Lte:
				qb = qb.Where(squirrel.LtOrEq{filter.Field: filter.Value})
			case Gt:
				qb = qb.Where(squirrel.Gt{filter.Field: filter.Value})
			case Gte:
				qb = qb.Where(squirrel.GtOrEq{filter.Field: filter.Value})
			default:
				// eq
				qb = qb.Where(squirrel.Eq{filter.Field: filter.Value})
			}
		}
	}

	return qb
}

func (c *CoreManager[T]) commonDeleteFilter(qb squirrel.DeleteBuilder, req *uchiha_models.CommonRequest) squirrel.DeleteBuilder {
	filters := req.Filters

	if len(filters) > 0 {
		for _, req := range filters {
			filter := &Filter{}
			utils.Map2Struct(req.AsMap(), filter)
			switch filter.Mode {
			case Lt:
				qb = qb.Where(squirrel.Lt{filter.Field: filter.Value})
			case Lte:
				qb = qb.Where(squirrel.LtOrEq{filter.Field: filter.Value})
			case Gt:
				qb = qb.Where(squirrel.Gt{filter.Field: filter.Value})
			case Gte:
				qb = qb.Where(squirrel.GtOrEq{filter.Field: filter.Value})
			default:
				// eq
				qb = qb.Where(squirrel.Eq{filter.Field: filter.Value})
			}
		}
	}

	return qb
}

func (c *CoreManager[T]) InsertObjectByTrx(sqlTool *mysql.SQLTool, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool.PrepareInsert(c.table(), ToProto[T](req.Model))

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList()).
		Values(sqlTool.GetFilledValues()...)

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}

	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQLTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.ExecTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}

	return response, nil
}

func (c *CoreManager[T]) InsertMultipleObjectByTrx(sqlTool *mysql.SQLTool, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	sqlTool.PrepareInsert(c.table(), &c.protoObject)

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList())

	for _, s := range req.List {
		qb = qb.Values(sqlTool.GetFillValues(s.AsMap()))
	}

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}

	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQLTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.ExecTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}

	return response, nil
}
