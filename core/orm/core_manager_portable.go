package uchiha_orm

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/Masterminds/squirrel"
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"github.com/protrip/uchiha-core/utils"
	"github.com/protrip/uchiha-core/utils/dms_utils"
)

type CoreManagerPortable struct {
	Mysql  *mysql.Mysql
	dbType string
}

func NewCoreManagerPortable(Mysql *mysql.Mysql, dbType string) *CoreManagerPortable {
	return &CoreManagerPortable{
		Mysql:  Mysql,
		dbType: dbType,
	}
}

type Insert struct {
	Id int64 `json:"id"`
}

func InsertObjectByOptions[T any](ctx context.Context, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool := mysql.NewInsert(ctx, c.Mysql, table(req.IgnoreColumns...), ToProto[T](req.Model))

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList()).
		Values(sqlTool.GetFilledValues()...)

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	qb = qb.Suffix(req.Suffix)

	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQL(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.Insert(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}

	return response, nil
}

func InsertMultipleObjectByOptions[T any](ctx context.Context, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := new(T)
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool := mysql.NewInsert(ctx, c.Mysql, table(), protoObject)

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList())

	list := ToSliceProto[T](req.List)
	for _, s := range list {
		qb = qb.Values(sqlTool.GetFillValues(s)...)
	}

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	qb = qb.Suffix(req.Suffix)

	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQL(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.Insert(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}
	return response, nil
}

func GetObjectsByOptions[T any](ctx context.Context, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	if len(req.JoinTables) > 0 || len(req.LeftJoinTables) > 0 {
		return GetObjectsWithJoinByOptions[T](ctx, c, req)
	}
	protoObject := new(T)

	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool := mysql.NewSelect(ctx, c.Mysql, table(req.IgnoreColumns...), protoObject)

	qb := squirrel.Select(sqlTool.GetQueryColumnList())
	if len(req.CustomizedSelect) > 0 {
		qb = squirrel.Select(req.CustomizedSelect)
	}

	qb = qb.From(sqlTool.GetTableName())

	qb = commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()

	if err != nil {
		return nil, err
	}

	list := make([]*T, 0)
	err = sqlTool.Select(&list, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		List: ToSliceStruct(list),
	}, nil
}

func GetObjectByOptions[T any](ctx context.Context, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := new(T)
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool := mysql.NewSelect(ctx, c.Mysql, table(req.IgnoreColumns...), protoObject)

	qb := squirrel.Select(sqlTool.GetQueryColumnList()).
		From(sqlTool.GetTableName())

	qb = commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	var s = new(T)
	err = sqlTool.Get(s, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		Model: ToStruct(s),
	}, nil
}

func GetObjectsWithJoinByOptions[T any](ctx context.Context, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := new(T)
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool := mysql.NewSelect(ctx, c.Mysql, table(req.IgnoreColumns...), protoObject)

	qb := squirrel.Select(sqlTool.GetQueryColumnListWithPrefix(req.TablePrefix))
	if len(req.CustomizedSelect) > 0 {
		qb = squirrel.Select(req.CustomizedSelect)
	}
	qb = qb.From(fmt.Sprintf("%v as %v", sqlTool.GetTableName(), req.TablePrefix))

	for _, tbj := range req.JoinTables {
		qb = qb.Join(fmt.Sprintf("%v as %v on %v", tbj.Name, tbj.Prefix, tbj.On))
	}

	for _, tbj := range req.LeftJoinTables {
		qb = qb.LeftJoin(fmt.Sprintf("%v as %v on %v", tbj.Name, tbj.Prefix, tbj.On))
	}

	qb = commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()

	if err != nil {
		return nil, err
	}

	list := make([]*T, 0)
	err = sqlTool.Select(&list, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		List: ToSliceStruct(list),
	}, nil
}

func UpdateObjectsByOptions[T any](ctx context.Context, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	protoObject := ToProto[T](req.Model)

	sqlTool := mysql.NewUpdateWithColumns(ctx, c.Mysql, table(req.IgnoreColumns...), protoObject, req.UpdateColumns)

	qb := squirrel.Update(sqlTool.GetTableName()).
		SetMap(sqlTool.GetUpdateMap())

	qb = commonUpdateFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	res, err := sqlTool.Update(query, args...)
	if err != nil {
		return nil, err
	}

	affected, _ := res.RowsAffected()
	return &uchiha_models.CommonResponse{
		RowsAffected: affected,
	}, nil
}

func DeleteObjectByOptions[T any](ctx context.Context, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := new(T)
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool := mysql.NewDelete(ctx, c.Mysql, table(), protoObject)

	qb := squirrel.Delete(sqlTool.GetTableName())

	qb = commonDeleteFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := sqlTool.Delete(query, args...)
	if err != nil {
		if dms_utils.IsDuplicateError(err) {
			return nil, mysql.ErrDuplicateData
		}
		if dms_utils.IsDeadlockError(err) {
			return nil, mysql.ErrDeadlock
		}
		return nil, err
	}

	rows, _ := result.RowsAffected()
	return &uchiha_models.CommonResponse{
		RowsAffected: rows,
	}, nil
}

func commonSelectFilter(qb squirrel.SelectBuilder, req *uchiha_models.CommonRequest) squirrel.SelectBuilder {
	filters := req.Filters

	if req.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Id})
	} else if len(req.Ids) > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Ids})
	}

	if req.MinId > 0 {
		qb = qb.Where(squirrel.Gt{"id": req.MinId})
	}

	if req.MaxId > 0 {
		qb = qb.Where(squirrel.LtOrEq{"id": req.MaxId})
	}

	if len(req.Name) > 0 {
		qb = qb.Where(squirrel.Eq{"name": req.Name})
	}

	if len(filters) > 0 {
		for _, req := range filters {
			filter := &Filter{}
			utils.Map2Struct(req.AsMap(), filter)
			switch filter.Mode {
			case Lt:
				qb = qb.Where(squirrel.Lt{filter.Field: filter.Value})
			case Lte:
				qb = qb.Where(squirrel.LtOrEq{filter.Field: filter.Value})
			case Gt:
				qb = qb.Where(squirrel.Gt{filter.Field: filter.Value})
			case Gte:
				qb = qb.Where(squirrel.GtOrEq{filter.Field: filter.Value})
			case Li:
				qb = qb.Where(squirrel.Like{filter.Field: "%" + filter.Value.(string) + "%"})
			case NLi:
				qb = qb.Where(squirrel.NotLike{filter.Field: "%" + filter.Value.(string) + "%"})
			default:
				// eq
				qb = qb.Where(squirrel.Eq{filter.Field: filter.Value})
			}
		}
	}

	if len(req.GroupBy) > 0 {
		qb = qb.GroupBy(req.GroupBy)
	}

	if len(req.OrderBy) > 0 {
		qb = qb.OrderBy(req.OrderBy)
	}

	limit := int64(250)
	if req.Limit > 0 {
		limit = req.Limit
	}

	qb = qb.Limit(uint64(limit))
	if req.Page > 0 {
		qb = qb.Offset(dms_utils.GetQueryOffset(uint64(limit), uint64(req.Page)))
	}

	return qb
}

func commonUpdateFilter(qb squirrel.UpdateBuilder, req *uchiha_models.CommonRequest) squirrel.UpdateBuilder {
	filters := req.Filters
	appliedFilter := false

	if req.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Id})
		appliedFilter = true
	} else if len(req.Ids) > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Ids})
		appliedFilter = true
	}

	if req.MinId > 0 {
		qb = qb.Where(squirrel.Gt{"id": req.MinId})
		appliedFilter = true
	}

	if req.MaxId > 0 {
		qb = qb.Where(squirrel.LtOrEq{"id": req.MaxId})
		appliedFilter = true
	}

	if len(req.Name) > 0 {
		qb = qb.Where(squirrel.Eq{"name": req.Name})
		appliedFilter = true
	}

	if len(filters) > 0 {
		appliedFilter = true
		for _, req := range filters {
			filter := &Filter{}
			utils.Map2Struct(req.AsMap(), filter)
			switch filter.Mode {
			case Lt:
				qb = qb.Where(squirrel.Lt{filter.Field: filter.Value})
			case Lte:
				qb = qb.Where(squirrel.LtOrEq{filter.Field: filter.Value})
			case Gt:
				qb = qb.Where(squirrel.Gt{filter.Field: filter.Value})
			case Gte:
				qb = qb.Where(squirrel.GtOrEq{filter.Field: filter.Value})
			case Li:
				qb = qb.Where(squirrel.Like{filter.Field: "%" + filter.Value.(string) + "%"})
			case NLi:
				qb = qb.Where(squirrel.NotLike{filter.Field: "%" + filter.Value.(string) + "%"})
			default:
				// eq
				qb = qb.Where(squirrel.Eq{filter.Field: filter.Value})
			}
		}
	}

	if !appliedFilter {
		uchiha_log.Logger().Fatal("No filter applied for update")
	}

	return qb
}

func commonDeleteFilter(qb squirrel.DeleteBuilder, req *uchiha_models.CommonRequest) squirrel.DeleteBuilder {
	filters := req.Filters

	appliedFilter := false
	if req.Id > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Id})
		appliedFilter = true
	} else if len(req.Ids) > 0 {
		qb = qb.Where(squirrel.Eq{"id": req.Ids})
		appliedFilter = true
	}

	if req.MinId > 0 {
		qb = qb.Where(squirrel.Gt{"id": req.MinId})
		appliedFilter = true
	}

	if req.MaxId > 0 {
		qb = qb.Where(squirrel.LtOrEq{"id": req.MaxId})
		appliedFilter = true
	}

	if len(req.Name) > 0 {
		qb = qb.Where(squirrel.Eq{"name": req.Name})
		appliedFilter = true
	}

	if len(filters) > 0 {
		appliedFilter = true
		for _, req := range filters {
			filter := &Filter{}
			utils.Map2Struct(req.AsMap(), filter)
			switch filter.Mode {
			case Lt:
				qb = qb.Where(squirrel.Lt{filter.Field: filter.Value})
			case Lte:
				qb = qb.Where(squirrel.LtOrEq{filter.Field: filter.Value})
			case Gt:
				qb = qb.Where(squirrel.Gt{filter.Field: filter.Value})
			case Gte:
				qb = qb.Where(squirrel.GtOrEq{filter.Field: filter.Value})
			case Li:
				qb = qb.Where(squirrel.Like{filter.Field: "%" + filter.Value.(string) + "%"})
			case NLi:
				qb = qb.Where(squirrel.NotLike{filter.Field: "%" + filter.Value.(string) + "%"})
			default:
				// eq
				qb = qb.Where(squirrel.Eq{filter.Field: filter.Value})
			}
		}
	}
	if !appliedFilter {
		uchiha_log.Logger().Fatal("No filter applied for delete")
	}

	return qb
}

func InsertObjectByTrx[T any](sqlTool *mysql.SQLTool, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool.PrepareInsert(table(), ToProto[T](req.Model))

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList()).
		Values(sqlTool.GetFilledValues()...)

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	qb = qb.Suffix(req.Suffix)

	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	// Perform insert
	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQLTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.ExecTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}

	return response, nil
}

func InsertMultipleObjectByTrx[T any](sqlTool *mysql.SQLTool, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := new(T)
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool.PrepareInsert(table(), protoObject)

	qb := squirrel.
		Insert(sqlTool.GetTableName()).
		Columns(sqlTool.GetQueryColumnList())

	list := ToSliceProto[T](req.List)
	for _, s := range list {
		qb = qb.Values(sqlTool.GetFillValues(s)...)
	}

	if c.dbType == Psql {
		req.Suffix += " RETURNING id"
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	qb = qb.Suffix(req.Suffix)

	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	response := &uchiha_models.CommonResponse{}
	switch c.dbType {
	case Psql:
		response.LastIds, err = sqlTool.InsertAsPSQLTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		if len(response.LastIds) > 0 {
			response.LastId = response.LastIds[0]
		}
	default:
		result, err := sqlTool.ExecTransQuery(query, args...)
		if err != nil {
			if dms_utils.IsDuplicateError(err) {
				return nil, mysql.ErrDuplicateData
			}
			if dms_utils.IsDeadlockError(err) {
				return nil, mysql.ErrDeadlock
			}
			return nil, err
		}
		response.LastId, _ = result.LastInsertId()
	}

	return response, nil
}

func GetObjectsByOptionsWithTrx[T any](sqlTool *mysql.SQLTool, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	if len(req.JoinTables) > 0 || len(req.LeftJoinTables) > 0 {
		return GetObjectsWithJoinByOptionsWithTrx[T](sqlTool, c, req)
	}
	protoObject := new(T)

	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool.PrepareSelect(table(req.IgnoreColumns...), protoObject)
	qb := squirrel.Select(sqlTool.GetQueryColumnList())
	if len(req.CustomizedSelect) > 0 {
		qb = squirrel.Select(req.CustomizedSelect)
	}

	qb = qb.From(sqlTool.GetTableName())

	qb = commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()

	if err != nil {
		return nil, err
	}

	list := make([]*T, 0)
	err = sqlTool.SelectTransQuery(&list, query, args...)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		List: ToSliceStruct(list),
	}, nil
}

func GetObjectsWithJoinByOptionsWithTrx[T any](sqlTool *mysql.SQLTool, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := new(T)
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	if len(req.TablePrefix) < 1 {
		return nil, fmt.Errorf("table prefix is required")
	}

	sqlTool.PrepareSelect(table(req.IgnoreColumns...), protoObject)

	qb := squirrel.Select(sqlTool.GetQueryColumnListWithPrefix(req.TablePrefix))
	if len(req.CustomizedSelect) > 0 {
		qb = squirrel.Select(req.CustomizedSelect)
	}
	qb = qb.From(fmt.Sprintf("%v as %v", sqlTool.GetTableName(), req.TablePrefix))

	for _, tbj := range req.JoinTables {
		qb = qb.Join(fmt.Sprintf("%v as %v on %v", tbj.Name, tbj.Prefix, tbj.On))
	}

	for _, tbj := range req.LeftJoinTables {
		qb = qb.LeftJoin(fmt.Sprintf("%v as %v on %v", tbj.Name, tbj.Prefix, tbj.On))
	}

	qb = commonSelectFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()

	if err != nil {
		return nil, err
	}

	list := make([]*T, 0)
	err = sqlTool.SelectTransQuery(&list, query, args...)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, mysql.ErrNotFoundData
		}
		return nil, err
	}

	return &uchiha_models.CommonResponse{
		List: ToSliceStruct(list),
	}, nil
}

func UpdateObjectsByOptionsWithTrx[T any](sqlTool *mysql.SQLTool, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	protoObject := ToProto[T](req.Model)

	sqlTool.PrepareUpdateWithColumns(table(), protoObject, req.UpdateColumns)

	qb := squirrel.Update(sqlTool.GetTableName()).
		SetMap(sqlTool.GetUpdateMap())

	qb = commonUpdateFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	res, err := sqlTool.ExecTransQuery(query, args...)
	if err != nil {
		return nil, err
	}

	affected, _ := res.RowsAffected()
	return &uchiha_models.CommonResponse{
		RowsAffected: affected,
	}, nil
}

func DeleteObjectByOptionsWithTrx[T any](sqlTool *mysql.SQLTool, c *CoreManagerPortable, req *uchiha_models.CommonRequest) (*uchiha_models.CommonResponse, error) {
	protoObject := new(T)
	table := GetTable[T]()
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	sqlTool.PrepareDelete(table(), protoObject)

	qb := squirrel.Delete(sqlTool.GetTableName())

	qb = commonDeleteFilter(qb, req)

	if c.dbType == Psql {
		qb = qb.PlaceholderFormat(squirrel.Dollar)
	}
	query, args, err := qb.ToSql()
	if err != nil {
		return nil, err
	}

	result, err := sqlTool.ExecTransQuery(query, args...)
	if err != nil {
		if dms_utils.IsDuplicateError(err) {
			return nil, mysql.ErrDuplicateData
		}
		if dms_utils.IsDeadlockError(err) {
			return nil, mysql.ErrDeadlock
		}
		return nil, err
	}

	rows, _ := result.RowsAffected()
	return &uchiha_models.CommonResponse{
		RowsAffected: rows,
	}, nil
}
