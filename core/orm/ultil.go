package uchiha_orm

import (
	"github.com/protrip/uchiha-core/utils"
	"google.golang.org/protobuf/types/known/structpb"
)

type Filter struct {
	Field string      `json:"field"`
	Mode  string      `json:"mode"`
	Value interface{} `json:"value"`
}

const (
	Eq  = "eq"
	Lt  = "lt"
	Lte = "lte"
	Gt  = "gt"
	Gte = "gte"
	Li  = "li"
	NLi = "nli"

	Mysql = "mysql"
	Psql  = "psql"
)

func GetFilter(list []*Filter) []*structpb.Struct {
	filter := make([]*structpb.Struct, 0)
	for _, f := range list {
		s, _ := structpb.NewStruct(utils.Struct2Map(f))
		filter = append(filter, s)
	}
	return filter
}

func ToStruct[T any](e T) *structpb.Struct {
	s, _ := structpb.NewStruct(utils.Struct2Map(e))
	return s
}

func ToProto[T any](e *structpb.Struct) *T {
	var s = new(T)
	utils.Map2Struct(e.AsMap(), s)
	return s
}

func ToSliceStruct[T any](input []T) []*structpb.Struct {
	res := make([]*structpb.Struct, 0)
	for _, e := range input {
		res = append(res, ToStruct[T](e))
	}
	return res
}

func ToSliceProto[T any](input []*structpb.Struct) []*T {
	res := make([]*T, 0)
	for _, e := range input {
		res = append(res, ToProto[T](e))
	}
	return res
}
