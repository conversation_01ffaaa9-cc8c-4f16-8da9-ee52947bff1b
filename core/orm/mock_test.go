package uchiha_orm

import (
	"context"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/bmizerany/assert"
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"github.com/protrip/uchiha-core/utils/dms_utils"
	"testing"

	"github.com/protrip/uchiha-core/core/drivers/mysql"
)

type MockModel struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

func MockTable(ignoreColumns ...string) mysql.Table {
	table := mysql.Table{
		Name:           "mock_model",
		AIColumns:      "id",
		NotNullColumns: []string{"id", "name"},
	}

	return dms_utils.GetTableWithIgnoreColumns2(table, ignoreColumns)
}

func TestInsertObjectByOptions(t *testing.T) {
	db, mock, err := sqlmock.New()
	mockDb, err := mysql.NewMysqlFromRawDB(db)
	if err != nil {
		t.Fatal(err)
	}

	RegisterTable[MockModel](MockTable)

	for _, testCase := range []struct {
		Label       string
		Model       *MockModel
		SetupExpect func(sqlmock.Sqlmock)
	}{
		{
			Label: "InsertObjectByOptions",
			SetupExpect: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("INSERT INTO mock_model *").
					ExpectExec().WithArgs(1, "test").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			Model: &MockModel{
				Id:   1,
				Name: "test",
			},
		},
	} {
		t.Run(testCase.Label, func(t *testing.T) {
			testCase.SetupExpect(mock)
			ctx := context.Background()
			portableManager := NewCoreManagerPortable(mockDb, "psql")

			a, err := InsertObjectByOptions[MockModel](ctx, portableManager, &uchiha_models.CommonRequest{
				Model: ToStruct(testCase.Model),
			})
			if err != nil {
				t.Fatal(err)
			}
			assert.Equal(t, a.LastId, testCase.Model.Id)
			assert.Equal(t, nil, mock.ExpectationsWereMet(), "unmet db expectations")
		})
	}
}

func TestInsertMultipleObjectByOptions(t *testing.T) {
	db, mock, err := sqlmock.New()
	mockDb, err := mysql.NewMysqlFromRawDB(db)
	if err != nil {
		t.Fatal(err)
	}

	RegisterTable[MockModel](MockTable)

	for _, testCase := range []struct {
		Label       string
		Models      []*MockModel
		SetupExpect func(sqlmock.Sqlmock)
	}{
		{
			Label: "InsertMultipleObjectByOptions",
			SetupExpect: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("INSERT INTO mock_model *").
					ExpectExec().WithArgs("test", "test2").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			Models: []*MockModel{
				{
					Id:   1,
					Name: "test",
				},
				{
					Id:   2,
					Name: "test2",
				},
			},
		},
	} {
		t.Run(testCase.Label, func(t *testing.T) {
			testCase.SetupExpect(mock)
			ctx := context.Background()
			portableManager := NewCoreManagerPortable(mockDb, "psql")

			a, err := InsertMultipleObjectByOptions[MockModel](ctx, portableManager, &uchiha_models.CommonRequest{
				List: ToSliceStruct(testCase.Models),
			})
			if err != nil {
				t.Fatal(err)
			}
			assert.T(t, a.LastId > 0)
			assert.Equal(t, nil, mock.ExpectationsWereMet(), "unmet db expectations")
		})
	}
}
