package uchiha_orm

import (
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	"reflect"
)

type TableManager struct {
	manager map[string]func(ignoreColumns ...string) mysql.Table
}

var (
	tableManager = &TableManager{}
)

func RegisterTable[T any](tableFunc func(ignoreColumns ...string) mysql.Table) {
	if tableManager.manager == nil {
		tableManager.manager = make(map[string]func(ignoreColumns ...string) mysql.Table)
	}

	object := new(T)
	t := reflect.TypeOf(object)
	tableManager.manager[t.Elem().Name()] = tableFunc
}

func GetTable[T any](ignoreColumns ...string) func(ignoreColumns ...string) mysql.Table {
	if tableManager.manager == nil || len(tableManager.manager) == 0 {
		return nil
	}
	object := new(T)
	t := reflect.TypeOf(object)
	return tableManager.manager[t.<PERSON>em().Name()]
}
