package config

import "flag"

type Options struct {
	ConfigType          string
	ConfigFile          string
	ConfigRemoteAddress string
	ConfigRemoteKeys    string

	// HTTP
	HTTPHost string
	HTTPPort int64

	DisplayName string
}

func InitDefaultFlags() *Options {
	o := &Options{}
	// flags for config
	flag.StringVar(&o.ConfigType, "config-type", "file", "Configuration type: file or remote")
	flag.StringVar(&o.ConfigFile, "config-file", "", "Configuration file")
	flag.StringVar(&o.ConfigRemoteAddress, "config-remote-address", "", "Configuration remote address. ip:port")
	flag.StringVar(&o.ConfigRemoteKeys, "config-remote-keys", "", "Configuration remote keys. Seperate by ,")

	//flag.BoolVar(&o.SdEnable, "sd-enable", true, "Enable register to service discovery or not")
	//flag.StringVar(&o.SdAddress, "sd-address", "127.0.0.1:8500", "Service discovery server address. ip:port")

	// flags for http
	flag.StringVar(&o.HTTPHost, "http-host", "localhost", "HTTP listen host")
	flag.Int64Var(&o.HTTPPort, "http-port", 8888, "HTTP listen port")

	// flags for grpc
	//flag.StringVar(&app.GrpcHost, "grpc-host", "", "grpc listen host")
	//flag.Int64Var(&app.GrpcPort, "grpc-port", 8899, "grpc listen port")

	//// cleanup
	//flag.BoolVar(&app.CleanupAtStart, "cleanup-at-start", false, "For cleaning up all the mess at start or not")

	// app name
	flag.StringVar(&o.DisplayName, "app-name", "", "App name")

	// enable profiling at start or not
	//flag.BoolVar(&app.EnableProfiling, "profiling", false, "Enable profiling from start or not")
	return o
}

func ParseFlag() {
	flag.Parse()
}
