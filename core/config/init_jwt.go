package config

import (
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/protrip/uchiha-core/utils"
	"github.com/spf13/viper"
	"io/ioutil"
	"strings"
)

func InitJWT(opt *Options) {

	configType := utils.StringTrimSpace(strings.ToLower(opt.ConfigType))
	//configRemoteAddress := utils.StringTrimSpace(opt.ConfigRemoteAddress)

	var err error
	var signBytes, verifyBytes, encryptKeyBytes []byte
	switch configType {
	case ConfigTypeFile:
		signBytes, err = ioutil.ReadFile(viper.GetString("jwt.private_key_path"))
		if err != nil {
			uchiha_log.Logger().Fatalf("Error get private key from file. Details: %v", err)
		}

		verifyBytes, err = ioutil.ReadFile(viper.GetString("jwt.public_key_path"))
		if err != nil {
			uchiha_log.Logger().Fatalf("Error get public key from file. Details: %v", err)
		}

		encryptKeyBytes, err = ioutil.ReadFile(viper.GetString("jwt.encrypt_key_path"))
		if err != nil {
			uchiha_log.Logger().Fatalf("Error get encrypt key from file. Details: %v", err)
		}

	case ConfigTypeRemote:
		//if utils.IsStringEmpty(configRemoteAddress) {
		//	uchiha_log.Logger().Fatal("Remote config server address is empty")
		//}
		//signBytes, err = config.GetFromConsulKV(app.ConfigRemoteAddress, "/jwt/private.key")
		//if err != nil {
		//	uchiha_log.Logger().Fatalf("Error get private key from remote config server. Details: %v", err)
		//}
		//
		//verifyBytes, err = config.GetFromConsulKV(app.ConfigRemoteAddress, "/jwt/public.key")
		//if err != nil {
		//	uchiha_log.Logger().Fatalf("Error get public key from remote config server. Details: %v", err)
		//}
		//
		//encryptKeyBytes, err = config.GetFromConsulKV(app.ConfigRemoteAddress, "/jwt/encrypt.key")
		//if err != nil {
		//	uchiha_log.Logger().Fatalf("Error get encrypt key from remote config server. Details: %v", err)
		//}

	}

	err = utils.InitJWT(signBytes, verifyBytes, encryptKeyBytes)
	if err != nil {
		uchiha_log.Logger().Fatalf("Could not load jwt. Detail %v", err)
	}
	uchiha_log.Logger().Info("JWT loaded")
}
