package config

import (
	"bytes"
	"fmt"
	"github.com/fsnotify/fsnotify"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/protrip/uchiha-core/utils"
	"github.com/spf13/viper"
)

const (
	ConfigTypeFile   = "file"
	ConfigTypeRemote = "remote"
)

func InitConfig(opt *Options) {
	configFile := utils.StringTrimSpace(opt.ConfigFile)
	//configRemoteAddress := utils.StringTrimSpace(opt.ConfigRemoteAddress)
	if len(opt.ConfigType) < 1 {
		uchiha_log.Logger().Info("Can not find config to init")
		return
	}
	configType := ConfigTypeFile
	if opt.ConfigType == ConfigTypeRemote {
		configType = ConfigTypeRemote
	}
	//configRemoteKeys := utils.StringSlice(
	//	utils.StringTrimSpace(opt.ConfigRemoteKeys), ",",
	//)

	result := false

	switch configType {
	case ConfigTypeFile:
		if utils.IsStringEmpty(configFile) {
			// read by default
			result = ReadConfig("conf", ".")
		} else {
			// read by input file
			result = ReadConfigByFile(configFile)
		}

	case ConfigTypeRemote:
		//if utils.IsStringEmpty(configRemoteAddress) {
		//	uchiha_log.Logger().Fatal("Remote config server address is empty")
		//}
		//if len(configRemoteKeys) < 1 {
		//	uchiha_log.Logger().Warn("This app has no config defined")
		//} else {
		//	for index := 0; index < len(configRemoteKeys); index++ {
		//		isMerge := true
		//		if index == 0 {
		//			isMerge = false
		//		}
		//		remoteKey := utils.StringTrimSpace(configRemoteKeys[index])
		//		if utils.IsStringEmpty(remoteKey) {
		//			continue
		//		}
		//		if remoteKey[0:1] != "/" {
		//			uchiha_log.Logger().Error(fmt.Sprintf("Invalid key: %v. Remote key must start with /", remoteKey))
		//			continue
		//		}
		//		valueBytes, err := config.GetFromConsulKV(app.ConfigRemoteAddress, remoteKey)
		//		if err != nil {
		//			logger.BkLog.Warnf("Could not get key %v from consul. Details: %v", remoteKey, err)
		//			continue
		//		}
		//		err = config.LoadConfig("toml", valueBytes, isMerge)
		//		if err != nil {
		//			logger.BkLog.Errorw(fmt.Sprintf("Could not load config from remote key %v. Details: %v", remoteKey, err))
		//			continue
		//		}
		//		logger.BkLog.Infof("Loaded config remote key %v", remoteKey)
		//	}
		//}

		result = true

	}

	if !result {
		uchiha_log.Logger().Fatal("Could not load config")
	}

	uchiha_log.Logger().Info("Config loaded")
}

func ReadConfig(fileName string, configPaths ...string) bool {
	viper.SetConfigName(fileName)
	if len(configPaths) < 1 {
		// look for current dir
		viper.AddConfigPath(".")
	} else {
		for _, configPath := range configPaths {
			viper.AddConfigPath(configPath)
		}
	}
	err := viper.ReadInConfig()
	if err != nil {
		fmt.Printf("Cannot read config file. %s", err)
		return false
	}

	viper.WatchConfig()
	viper.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("Config file changed:", e.Name)
	})

	return true
}

func ReadConfigByFile(file string) bool {
	viper.SetConfigFile(file)
	err := viper.ReadInConfig()
	if err != nil {
		fmt.Printf("Cannot read config file. %s", err)
		return false
	}

	viper.WatchConfig()
	viper.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("Config file changed:", e.Name)
	})

	return true
}

// LoadConfig -- read config from byte
func LoadConfig(configType string, value []byte, isMerge bool) (err error) {
	viper.SetConfigType(configType)
	if !isMerge {
		err = viper.ReadConfig(bytes.NewBuffer(value))
	} else {
		err = viper.MergeConfig(bytes.NewBuffer(value))
	}
	return
}
