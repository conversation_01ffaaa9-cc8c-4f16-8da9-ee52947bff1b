package object_provider

import (
	"errors"
	"reflect"
)

type provider struct {
	storage      map[string]any
	constructors map[string]func() any
}

var p *provider

func newObjectProvider() {
	if p != nil {
		return
	}
	p = &provider{}
	p.storage = make(map[string]any)
	p.constructors = make(map[string]func() any)
}

type Option struct {
	customName string
}

type OptionFunc func(o *Option)

func WithName(name string) OptionFunc {
	return func(o *Option) {
		o.customName = name
	}
}

func initProvider(options ...OptionFunc) *Option {
	if p == nil {
		newObjectProvider()
	}
	o := &Option{}
	for _, opt := range options {
		opt(o)
	}
	return o
}

func Provide(obj any, options ...OptionFunc) error {
	o := initProvider(options...)
	t := reflect.TypeOf(obj)
	if !(t.Kind() == reflect.Ptr && t.Elem().Kind() == reflect.Struct) {
		return ErrNotPointer
	}
	name := t.Elem().Name()
	if len(o.customName) > 0 {
		name = o.customName
	}

	if isDuplicate(name) {
		return ErrDuplicate
	}
	p.storage[name] = obj
	return nil
}

func ProvideConstructor[T any](f func() *T, options ...OptionFunc) error {
	o := initProvider(options...)
	t := reflect.TypeFor[T]()
	name := t.Name()
	if len(o.customName) > 0 {
		name = o.customName
	}
	if isDuplicate(name) {
		return ErrDuplicate
	}

	p.constructors[name] = func() any {
		return f()
	}
	return nil
}

func isDuplicate(name string) bool {
	return p.storage[name] != nil || p.constructors[name] != nil
}

func Supply[T any](options ...OptionFunc) (*T, error) {
	o := initProvider(options...)
	t := reflect.TypeFor[T]()
	structName := t.Name()
	if len(o.customName) > 0 {
		structName = o.customName
	}
	objInStorage, ok1 := p.storage[structName]
	constructor, ok2 := p.constructors[structName]
	if !ok1 && !ok2 {
		return new(T), ErrNotfound
	}
	if ok1 {
		object, ok := objInStorage.(*T)
		if !ok {
			return nil, ErrConstructorFailed
		}

		return object, nil
	}

	newT, ok := (constructor()).(*T)
	if !ok {
		return nil, ErrConstructorFailed
	}
	return newT, nil
}

var (
	ErrDuplicate         = errors.New("duplicate")
	ErrNotfound          = errors.New("notfound")
	ErrNotPointer        = errors.New("not_pointer")
	ErrConstructorFailed = errors.New("constructor_failed")
)
