package object_provider

import (
	"github.com/bmizerany/assert"
	"testing"
)

type A struct {
}

func NewA() *A {
	return &A{}
}

type B struct {
}

func NewB() *B {
	return &B{}
}

type C struct {
}

func (c C) Name() string {
	return "A"
}
func NewC() *C {
	return &C{}
}

type D struct {
}

func (d D) Name() string {
	return "D"
}

type E struct {
}

func TestObjectProvider(t *testing.T) {
	a := A{}
	c := &C{}
	err := Provide(a)
	assert.Equal(t, err, ErrNotPointer)

	err = Provide(&a)
	assert.Equal(t, err, nil)

	objA, err := Supply[A]()
	assert.Equal(t, err, nil)
	assert.Equal(t, objA, &a)

	err = Provide(c)
	assert.Equal(t, err, nil)
	err = Provide(c)
	assert.Equal(t, err, ErrDuplicate)

	_, err = Supply[D]()
	assert.Equal(t, err, ErrNotfound)

	_, err = Supply[E]()
	assert.Equal(t, err, <PERSON>rr<PERSON>otfound)

}

func TestObjectConstructor(t *testing.T) {

	err := ProvideConstructor(NewA)
	assert.Equal(t, err, nil)

	err = ProvideConstructor(NewB, WithName("A"))
	assert.Equal(t, err, ErrDuplicate)

	err = ProvideConstructor(NewC, WithName("B"))
	assert.Equal(t, err, nil)

	_, err = Supply[B]()
	if err != nil {
		assert.Equal(t, err, ErrConstructorFailed)
	}

	objC, err := Supply[C](WithName("B"))
	assert.Equal(t, err, nil)
	assert.Equal(t, objC, NewC())
}
