package mysql

import (
	"database/sql"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"

	// postgre driver
	_ "github.com/lib/pq"
)

// NewPostgreConnection -- open connection to db
func NewPostgreConnection(conn Connection) (*Mysql, error) {
	var err error

	settings, _ := conn.BuildSetting()

	db, err := sql.Open("postgres", settings[0])
	if err != nil {
		uchiha_log.Logger().Error("[postgres] Could not connect database, details: ", err)
		return nil, err
	}
	if conn.GetMaxOpenConn() > 0 {
		db.SetMaxOpenConns(conn.GetMaxOpenConn())
	}
	if conn.GetMaxIdleConn() > 0 && conn.GetMaxIdleConn() < conn.GetMaxOpenConn() {
		db.SetMaxIdleConns(conn.GetMaxIdleConn())
	}
	db.SetConnMaxLifetime(conn.GetConnMaxLifetime())

	err = db.<PERSON>()
	if err != nil {
		uchiha_log.Logger().Error("[postgres] Could not ping to database, details: ", err)
		return nil, err
	}

	return NewMysqlFromRawDB(db)
}
