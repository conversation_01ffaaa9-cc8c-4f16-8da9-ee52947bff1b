package mysql

import (
	"database/sql"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"github.com/pkg/errors"
	"github.com/protrip/uchiha-core/core/drivers/sqlhooks"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"time"
)

// Mysql --
type Mysql struct {
	*DB
}

// NewConnection -- open connection to db
func NewConnection(conn Connection) (*Mysql, error) {
	driverName := "mysql"
	if conn.GetHook() != nil {
		driverName := fmt.Sprintf("mysql-hook-%s", time.Now().String())
		sql.Register(driverName, sqlhooks.Wrap(&mysql.MySQLDriver{}, conn.GetHook()))
	}

	masterSetting, slaveSetting := conn.BuildSetting()
	db, err := Open(driverName, masterSetting, slaveSetting)

	if err != nil {
		uchiha_log.Logger().Error("[mysql] Could not connect database, details: ", err)
		return nil, err
	}
	if conn.GetMaxOpenConn() > 0 {
		db.SetMaxOpenConns(conn.GetMaxOpenConn())
	}
	if conn.GetMaxIdleConn() > 0 && conn.GetMaxIdleConn() < conn.GetMaxOpenConn() {
		db.SetMaxIdleConns(conn.GetMaxIdleConn())
	}
	db.SetConnMaxLifetime(conn.GetConnMaxLifetime())

	err = db.Ping()
	if err != nil {
		uchiha_log.Logger().Error("[mysql] Could not ping to database, details: ", err)
		return nil, err
	}

	return NewMysql(db)
}

func NewMysql(db *DB) (*Mysql, error) {
	if db == nil {
		return nil, errors.New("nil db is provided")
	}

	return &Mysql{
		DB: db,
	}, nil
}

func NewMysqlFromRawDB(db *sql.DB) (*Mysql, error) {
	if db == nil {
		return nil, errors.New("nil db is provided")
	}

	return &Mysql{
		DB: NewDB([]*SingleDB{NewSingleDB(db, 0)}, nil),
	}, nil
}

// Close -- close connection
func (c *Mysql) Close() {
	if c == nil {
		return
	}

	if err := c.DB.Close(); err != nil {
		uchiha_log.Logger().Error(fmt.Sprintf("Error when close db: %v", err))
	}
}

// GetDB -- get db
func (c *Mysql) GetDB() *sql.DB {
	return c.DB.GetMaster()
}
