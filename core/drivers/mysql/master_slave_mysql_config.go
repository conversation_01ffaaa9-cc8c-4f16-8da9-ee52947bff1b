package mysql

import (
	"fmt"
	"github.com/protrip/uchiha-core/core/drivers/sqlhooks"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	utils2 "github.com/protrip/uchiha-core/utils"
	"github.com/spf13/viper"
	"time"
)

type MasterSlaveDBConnection struct {
	BaseConnection
	MasterAddresses []string
	SlaveAddresses  []string
}

func NewMasterSlaveConn(base BaseConnection) *MasterSlaveDBConnection {
	return &MasterSlaveDBConnection{
		BaseConnection:  base,
		MasterAddresses: viper.GetStringSlice("mysql.master_addresses"),
		SlaveAddresses:  viper.GetStringSlice("mysql.slave_addresses"),
	}
}

func (c *MasterSlaveDBConnection) BuildSetting() ([]string, []string) {
	listMasterSetting := make([]string, 0)
	for _, addr := range c.MasterAddresses {
		host, port := utils2.ParseAddr(addr)
		settings := fmt.Sprintf("%s:%s@%s(%s:%d)/%s?charset=%s&parseTime=%t",
			c.User, c.Password,
			c.Protocol,
			host, port,
			c.DatabaseName,
			c.Charset, c.ParseTime,
		)
		if utils2.IsStringNotEmpty(c.Others) {
			settings = fmt.Sprintf("%s&%s", settings, c.Others)
		}
		listMasterSetting = append(listMasterSetting, settings)

		logSettings := fmt.Sprintf("%s:%s@%s(%s:%d)/%s?charset=%s&parseTime=%t",
			c.User, utils2.CensorString(c.Password),
			c.Protocol,
			host, port,
			c.DatabaseName,
			c.Charset, c.ParseTime,
		)

		uchiha_log.Logger().Info("[mysql] Connecting with this configuration: ", logSettings)
	}

	listSlaveSetting := make([]string, 0)
	for _, addr := range c.SlaveAddresses {
		host, port := utils2.ParseAddr(addr)
		settings := fmt.Sprintf("%s:%s@%s(%s:%d)/%s?charset=%s&parseTime=%t",
			c.User, c.Password,
			c.Protocol,
			host, port,
			c.DatabaseName,
			c.Charset, c.ParseTime,
		)
		if utils2.IsStringNotEmpty(c.Others) {
			settings = fmt.Sprintf("%s&%s", settings, c.Others)
		}
		listSlaveSetting = append(listSlaveSetting, settings)

		logSettings := fmt.Sprintf("%s:%s@%s(%s:%d)/%s?charset=%s&parseTime=%t",
			c.User, utils2.CensorString(c.Password),
			c.Protocol,
			host, port,
			c.DatabaseName,
			c.Charset, c.ParseTime,
		)

		uchiha_log.Logger().Info("[mysql] Connecting with this configuration: ", logSettings)
	}

	return listMasterSetting, listSlaveSetting
}

func (c *MasterSlaveDBConnection) GetHook() sqlhooks.Hooks {
	return c.Hooks
}
func (c *MasterSlaveDBConnection) GetMaxIdleConn() int {
	return c.MaxIdleConn
}

func (c *MasterSlaveDBConnection) GetMaxOpenConn() int {
	return c.MaxOpenConn
}
func (c *MasterSlaveDBConnection) GetConnMaxLifetime() time.Duration {
	return time.Duration(c.ConnectionLifeTime) * time.Second
}
