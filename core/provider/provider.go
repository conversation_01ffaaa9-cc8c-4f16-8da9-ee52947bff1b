package provider

//func Logger() *logger.Helper {
//	uLog, err := object_provider.Supply[logger.Helper]()
//	if err != nil {
//		logger.DefaultHelper.Info("Error: ", err.Error())
//		if errors.Is(err, object_provider.ErrNotfound) {
//			uLog = logger.NewHelper(uchiha_log.NewLogger())
//			err = object_provider.Provide(uLog)
//			if err != nil {
//				panic(fmt.Sprintf("Error when providing logger: %v", err.Error()))
//			}
//		} else {
//			panic(fmt.Sprintf("Error when getting logger: %v", err.Error()))
//		}
//	}
//	return uLog
//}
