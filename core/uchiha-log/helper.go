package uchiha_log

import (
	"context"
	"go-micro.dev/v5/logger"
	"os"
)

type Log struct {
	logger logger.Logger
}

func NewHelper(logger logger.Logger) *Log {
	return &Log{logger: logger}
}

// Extract always returns valid Log with logger from context or with <PERSON><PERSON><PERSON><PERSON>og<PERSON> as fallback.
// Can be used in pair with function Inject.
// Example: propagate RequestID to logger in service handler methods.
func Extract(ctx context.Context) *Log {
	if l, ok := FromContext(ctx); ok {
		return NewHelper(l)
	}

	return NewHelper(logger.DefaultLogger)
}

func (h *Log) ToContext(ctx context.Context) context.Context {
	return NewContext(ctx, h.logger)
}

func (h *Log) Inject(ctx context.Context) context.Context {
	return NewContext(ctx, h.logger)
}

func (h *Log) Log(level logger.Level, args ...interface{}) {
	h.logger.Log(level, args...)
}

func (h *Log) Logf(level logger.Level, template string, args ...interface{}) {
	h.logger.Logf(level, template, args...)
}

func (h *Log) Info(args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.InfoLevel) {
		return
	}
	h.logger.Log(logger.InfoLevel, args...)
}

func (h *Log) Infof(template string, args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.InfoLevel) {
		return
	}
	h.logger.Logf(logger.InfoLevel, template, args...)
}

func (h *Log) Trace(args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.TraceLevel) {
		return
	}
	h.logger.Log(logger.TraceLevel, args...)
}

func (h *Log) Tracef(template string, args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.TraceLevel) {
		return
	}
	h.logger.Logf(logger.TraceLevel, template, args...)
}

func (h *Log) Debug(args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.DebugLevel) {
		return
	}
	h.logger.Log(logger.DebugLevel, args...)
}

func (h *Log) Debugf(template string, args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.DebugLevel) {
		return
	}
	h.logger.Logf(logger.DebugLevel, template, args...)
}

func (h *Log) Warn(args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.WarnLevel) {
		return
	}
	h.logger.Log(logger.WarnLevel, args...)
}

func (h *Log) Warnf(template string, args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.WarnLevel) {
		return
	}
	h.logger.Logf(logger.WarnLevel, template, args...)
}

func (h *Log) Error(args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.ErrorLevel) {
		return
	}
	h.logger.Log(logger.ErrorLevel, args...)
}

func (h *Log) Errorf(template string, args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.ErrorLevel) {
		return
	}
	h.logger.Logf(logger.ErrorLevel, template, args...)
}

func (h *Log) Fatal(args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.FatalLevel) {
		return
	}
	h.logger.Log(logger.FatalLevel, args...)
	os.Exit(1)
}

func (h *Log) Fatalf(template string, args ...interface{}) {
	if !h.logger.Options().Level.Enabled(logger.FatalLevel) {
		return
	}
	h.logger.Logf(logger.FatalLevel, template, args...)
	os.Exit(1)
}

func (h *Log) WithError(err error) *Log {
	return &Log{logger: h.logger.Fields(map[string]interface{}{"error": err})}
}

func (h *Log) WithFields(fields map[string]interface{}) *Log {
	return &Log{logger: h.logger.Fields(fields)}
}
