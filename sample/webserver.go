package main

import (
	"net/http"

	handler "baber-be/sample/handlers/booking-handler"

	"github.com/protrip/uchiha-core/core/transport/transhttp"
)

type server struct {
	name     string
	basePath string
}

func NewServer(name, basePath string) *server {
	return &server{name: name, basePath: basePath}
}

func (s *server) Name() string {
	return s.name
}

func (s *server) Routes() transhttp.Routes {
	var routes transhttp.Routes

	routes = append(routes,
		s.initBookingRoutes()...,
	)

	return routes
}

func (s *server) initBookingRoutes() transhttp.Routes {
	getBookingInfo := transhttp.Route{
		Name:     "GetBookingInfo",
		Method:   http.MethodGet,
		BasePath: s.basePath,
		Pattern:  "/booking",
		Handler:  &handler.GetBookingHandler{}}

	return transhttp.Routes{
		getBookingInfo,
	}
}
