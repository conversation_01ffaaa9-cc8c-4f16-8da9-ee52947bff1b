package handler

import (
	"net/http"

	"github.com/protrip/uchiha-core/core/transport/transhttp"
)

type GetBookingHandler struct {
}

type GetBookingResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message"`
	ErrorMessage string `json:"error_message,omitempty"`
}

func (g *GetBookingHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	response := GetBookingResponse{
		Success: true,
		Message: "Get booking successfully",
	}
	transhttp.RespondJSON(w, http.StatusOK, response)
}
