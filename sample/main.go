package main

import (
	"go-micro.dev/v5/logger"
	"go-micro.dev/v5/web"
	"sync"

	webCore "github.com/protrip/uchiha-core/web"
)

func main() {
	s := NewServer("barber-admin", "/api/v1/admin")

	w := web.NewService(
		web.HandleSignal(true),
		web.Address("localhost:8080"),
		web.Handler(webCore.NewHandler(s.Routes(), s.Name())),
	)

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		err := w.Run()
		if err != nil {
			logger.Logf(logger.ErrorLevel, "web run error: %v", err)
		}
	}()

	wg.Wait()
}
