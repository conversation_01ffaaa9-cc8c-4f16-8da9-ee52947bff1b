package utils

import (
	"github.com/protrip/uchiha-core/core/drivers/mysql"
	"google.golang.org/grpc/status"
	"io"
)

func IsNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	return status.Convert(err).Message() == mysql.ErrNotFoundData.Error() || err.Error() == io.EOF.Error()

}

func IsDuplicateError(err error) bool {
	if err == nil {
		return false
	}
	return status.Convert(err).Message() == mysql.ErrDuplicateData.Error()
}

func IsDeadlockError(err error) bool {
	if err == nil {
		return false
	}
	return status.Convert(err).Message() == mysql.ErrDeadlock.Error()
}
