package utils

import "github.com/spf13/cast"

func StringSliceToInt64(slice []string) []int64 {
	res := make([]int64, 0)
	for _, s := range slice {
		res = append(res, cast.ToInt64(s))
	}
	return res
}

func Unique[T comparable](slice []T) []T {
	seen := make(map[T]struct{})
	unique := make([]T, 0, len(slice))

	for _, item := range slice {
		if _, exists := seen[item]; !exists {
			seen[item] = struct{}{}
			unique = append(unique, item)
		}
	}

	return unique
}
