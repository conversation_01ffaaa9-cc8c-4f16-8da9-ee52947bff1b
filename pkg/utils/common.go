package utils

import (
	"errors"
	"fmt"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/redis/go-redis/v9"
	"time"
)

func DoSomethingWithRetry(doAction func() (interface{}, error), timesRetry int, description string) interface{} {
	for i := 0; i < timesRetry; i++ {
		res, err := doAction()
		if err == nil || errors.Is(err, redis.Nil) {
			return res
		}
		uchiha_log.Logger().Error(fmt.Sprintf("Do %v caught err", description), "err", err)
		time.Sleep(200)
	}
	return nil
}
