package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/protrip/uchiha-core/core/tokenmanager"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"io/ioutil"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/spf13/cast"
)

const (
	RequestDateTimeLayout  = "2006-01-02T15:04:05-07:00"
	RequestDateTimeLayout2 = "2006-01-02T15:04:05Z"
	RequestDateTimeLayout3 = "2006-01-02T15:04:05+07:00"
)

var RequestDateTimeLayouts = []string{
	RequestDateTimeLayout,
	RequestDateTimeLayout2,
	RequestDateTimeLayout3,
}

type CommonParameters struct {
	Id              int64
	ShopId          int64
	AppId           int64
	ThirdAppId      int64
	PrivateAppId    int64
	Ids             []int64
	SinceId         int64
	CreatedAtMin    *time.Time
	CreatedAtMax    *time.Time
	UpdatedAtMin    *time.Time
	UpdatedAtMax    *time.Time
	PublishedAtMin  *time.Time
	PublishedAtMax  *time.Time
	Limit           int64
	Page            int64
	Fields          []string
	AggregateFields []string
	SortField       string
	SortMode        string
	Status          string
	ExcludeIds      []int64
	ShopType        string // printbase?
	Ctx             context.Context
}

// Use this function to get all of common parameters like ids, since_id, ... and more
func GetCommonParametersFromRequest(r *http.Request) (parameters CommonParameters, err error) {
	urlQuery := r.URL.Query()

	parameters.Ctx = r.Context()
	parameters.ShopId = GetShopIdFromRequest(r)
	parameters.AppId = GetAppIdFromRequest(r)
	parameters.SinceId = cast.ToInt64(urlQuery.Get("since_id"))
	parameters.Limit = cast.ToInt64(urlQuery.Get("limit"))
	parameters.Page = cast.ToInt64(urlQuery.Get("page"))
	parameters.Fields = ParseRawFields(urlQuery.Get("fields"))
	parameters.AggregateFields = ParseRawFields(urlQuery.Get("aggregate_fields"))
	parameters.SortField = ValidateSortField(urlQuery.Get("sort_field"))
	parameters.SortMode = ValidateSortMode(urlQuery.Get("sort_mode"))
	parameters.Id = cast.ToInt64(urlQuery.Get("id"))
	parameters.Status = urlQuery.Get("status")
	parameters.ExcludeIds, _ = ParseRawIds(urlQuery.Get("exclude_ids"))

	parameters.CreatedAtMin = ParseRawDateTime(urlQuery.Get("created_at_min"))
	parameters.CreatedAtMax = ParseRawDateTime(urlQuery.Get("created_at_max"))
	parameters.UpdatedAtMin = ParseRawDateTime(urlQuery.Get("updated_at_min"))
	parameters.UpdatedAtMax = ParseRawDateTime(urlQuery.Get("updated_at_max"))
	parameters.PublishedAtMin = ParseRawDateTime(urlQuery.Get("published_at_min"))
	parameters.PublishedAtMax = ParseRawDateTime(urlQuery.Get("published_at_max"))

	// ids
	idsParam := r.URL.Query().Get("ids")
	if idsParam != "" {
		ids, err := ParseRawIds(idsParam)
		if err != nil {
			return parameters, err
		}
		parameters.Ids = ids
	}

	// ExcludeIds
	excludeIdsParam := r.URL.Query().Get("exclude_ids")
	if excludeIdsParam != "" {
		excludeIds, err := ParseRawIds(excludeIdsParam)
		if err != nil {
			return parameters, err
		}
		parameters.ExcludeIds = excludeIds
	}

	return
}

func ValidateSortField(sortField string) string {
	if sortField == "" {
		return sortField
	}

	validated, _ := regexp.MatchString(`^[a-zA-Z0-9_]*$`, sortField)
	if !validated {
		uchiha_log.Logger().Warnf("Sort field invalid: %s", sortField)
		return ""
	}

	return sortField
}

func ValidateSortMode(sortMode string) string {
	if sortMode == "" {
		return sortMode
	}

	if strings.ToUpper(sortMode) != "DESC" && strings.ToUpper(sortMode) != "ASC" {
		return ""
	}

	return sortMode
}

func GetShopIdFromRequest(r *http.Request) int64 {
	shopId := cast.ToInt64(r.Header.Get("X-Shop-ID"))
	if shopId == 0 {
		shopId = cast.ToInt64(r.URL.Query().Get("shop_id"))
	}
	return shopId
}

func GetAppIdFromRequest(r *http.Request) int64 {
	appId := cast.ToInt64(r.Header.Get("X-App-ID"))
	if appId == 0 {
		appId = cast.ToInt64(r.URL.Query().Get("app_id"))
	}
	return appId
}

func GetUserIdFromRequest(r *http.Request) int64 {
	userId := cast.ToInt64(r.Header.Get("X-User-ID"))
	if userId == 0 {
		userId = cast.ToInt64(r.URL.Query().Get("user_id"))
	}
	return userId
}

func GetAdminUserEmailFromRequest(r *http.Request) string {
	return r.Header.Get("X-User-Email")
}

func ParseRawIds(rawIds string) (ids []int64, err error) {
	parts := strings.Split(rawIds, ",")

	for _, id := range parts {
		id = strings.Trim(id, " ")
		idInt64, err := getValueFromParam(id)
		if err != nil {
			return []int64{}, err
		}
		ids = append(ids, idInt64)
	}

	return
}

func ParseRawFields(rawFields string) (fields []string) {
	parts := strings.Split(rawFields, ",")

	for _, field := range parts {
		field = strings.Trim(field, " ")

		if len(field) > 0 {
			fields = append(fields, field)
		}
	}

	return
}

func ParseRawDateTime(rawDateTime string) (t *time.Time) {
	if len(rawDateTime) > 0 {
		for _, layout := range RequestDateTimeLayouts {
			timeParsed, err := time.Parse(layout, rawDateTime)

			if err == nil {
				return &timeParsed
			}
		}
	}

	return nil
}

func ParseRawDateTimeISO8601(rawDateTime string) (t *time.Time) {
	if len(rawDateTime) > 0 {
		timeParsed, err := time.Parse(RequestDateTimeLayout2, rawDateTime)

		if err == nil {
			return &timeParsed
		}

		uchiha_log.Logger().Warn("Date time format is invalid", "raw_date_time", rawDateTime, "error", err)
	}

	return nil
}

func GetTimeFromUnixTimestamp(unix int64) *time.Time {
	if unix == 0 {
		return nil
	}

	datetime := time.Unix(unix, 0)

	return &datetime
}

func GetUnixTimestamp(t *time.Time) int64 {
	if t != nil && !t.IsZero() {
		return t.Unix()
	}

	return 0
}

func GetUnixTimestampFromString(rawDateTime string) int64 {
	return GetUnixTimestamp(ParseRawDateTime(rawDateTime))
}

func GetIntFromRequestHeader(r *http.Request, field string) int64 {
	queryString := r.Header.Get(field)

	if len(queryString) > 0 {
		return cast.ToInt64(queryString)
	}

	return 0
}

func GetIntFromRequestQuery(r *http.Request, field string, defaultValue int64) int64 {
	queryString := r.URL.Query().Get(field)

	if len(queryString) > 0 {
		return cast.ToInt64(queryString)
	}

	return defaultValue
}

func GetFloatFromRequestQuery(r *http.Request, field string, defaultValue float64) float64 {
	queryString := r.URL.Query().Get(field)

	if len(queryString) > 0 {
		return cast.ToFloat64(queryString)
	}

	return defaultValue
}

func GetBoolFromRequestQuery(r *http.Request, field string) bool {
	queryString := r.URL.Query().Get(field)

	if len(queryString) > 0 {
		return cast.ToBool(queryString)
	}

	return false
}

func GetIntFromRequestParams(r *http.Request, field string) int64 {
	paramString := mux.Vars(r)[field]

	if len(paramString) > 0 {
		return cast.ToInt64(paramString)
	}

	return 0
}

func GetFloatFromRequestParams(r *http.Request, field string) float64 {
	paramString := mux.Vars(r)[field]

	if len(paramString) > 0 {
		return cast.ToFloat64(paramString)
	}

	return 0
}

func GetUnixTimestampFromRequest(r *http.Request, field string) int64 {
	return GetUnixTimestamp(ParseRawDateTime(r.URL.Query().Get(field)))
}

func GetRequestBodyData(r *http.Request) (map[string]interface{}, error) {
	bodyData, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}

	var jsonData map[string]interface{}

	err = json.Unmarshal(bodyData, &jsonData)
	if err != nil {
		return nil, err
	}

	return jsonData, nil
}

func GetRequestBodyDataDeep(r *http.Request) (map[string]map[string]interface{}, error) {
	bodyData, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}

	var jsonData map[string]map[string]interface{}

	err = json.Unmarshal(bodyData, &jsonData)
	if err != nil {
		return nil, err
	}

	return jsonData, nil
}

func getValueFromParam(p string) (int64, error) {
	return strconv.ParseInt(p, 10, 64)
}

func GetAccessToken(r *http.Request) (token string, err error) {
	buf, _ := ioutil.ReadAll(r.Body)
	token, err = tokenmanager.OAuth2Extractor.ExtractToken(r)
	r.MultipartForm = nil
	r.Body = ioutil.NopCloser(bytes.NewBuffer(buf))
	return
}

func GetUnixTimeStampStartTimeFromISO(timeStamp int64, timeZone string) (int64, error) {
	ts := time.Unix(timeStamp, 0)

	loc := GetLocationTimeZone(timeZone)
	_, offset := ts.In(loc).Zone()
	ts = ts.Truncate(24 * time.Hour)
	return ts.Unix() - int64(offset), nil
}

func GetLocationTimeZone(location string) *time.Location {
	if location == "" {
		return time.UTC
	}

	loc, err := time.LoadLocation(location)
	if err != nil {
		return time.UTC
	}

	return loc
}

func GetNestedUpdateFields(r *http.Request, field string, lIgnoreUpdateFields []string) []string {
	ignoreUpdateFields := make(map[string]bool)
	for _, s := range lIgnoreUpdateFields {
		ignoreUpdateFields[s] = true
	}
	resp := make([]string, 0)
	m := make(map[string]map[string]interface{})
	buf, _ := ioutil.ReadAll(r.Body)
	_ = json.Unmarshal(buf, &m)
	r.MultipartForm = nil
	r.Body = ioutil.NopCloser(bytes.NewBuffer(buf))

	for k, job := range m {
		if k != field {
			continue
		}

		for field := range job {
			if ignoreUpdateFields[field] {
				continue
			}
			resp = append(resp, field)
		}
	}

	return resp
}

func GetUpdateFields(r *http.Request, lIgnoreUpdateFields []string) []string {
	ignoreUpdateFields := make(map[string]bool)
	for _, s := range lIgnoreUpdateFields {
		ignoreUpdateFields[s] = true
	}

	resp := make([]string, 0)
	m := make(map[string]interface{})
	buf, _ := ioutil.ReadAll(r.Body)
	_ = json.Unmarshal(buf, &m)
	r.MultipartForm = nil
	r.Body = ioutil.NopCloser(bytes.NewBuffer(buf))

	for k, _ := range m {
		if ignoreUpdateFields[k] {
			continue
		}
		resp = append(resp, k)
	}

	return resp
}
