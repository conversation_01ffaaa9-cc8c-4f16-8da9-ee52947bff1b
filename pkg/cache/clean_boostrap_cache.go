package cache

import (
	"context"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
)

func CleanServiceCache() {
	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply redis", "err", err.Error())
		return
	}
	rErr := redisClient.Del(context.Background(), "bootstrap:services").Err()
	if rErr != nil {
		uchiha_log.Logger().Error("Can not delete bootstrap:services", "err", rErr.Error())
	}
}

func CleanStaffCache() {
	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply redis", "err", err.Error())
		return
	}
	rErr := redisClient.Del(context.Background(), "bootstrap:staffs").Err()
	if rErr != nil {
		uchiha_log.Logger().Error("Can not delete bootstrap:staffs", "err", rErr.Error())
	}
}

func CleanStaffOffCache() {
	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply redis", "err", err.Error())
		return
	}
	rErr := redisClient.Del(context.Background(), "bootstrap:staff_offs").Err()
	if rErr != nil {
		uchiha_log.Logger().Error("Can not delete bootstrap:staff_offs", "err", rErr.Error())
	}
}

func CleanServiceCustom() {
	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Error("Can not supply redis", "err", err.Error())
		return
	}
	rErr := redisClient.Del(context.Background(), "bootstrap:service_customs").Err()
	if rErr != nil {
		uchiha_log.Logger().Error("Can not delete bootstrap:service_customs", "err", rErr.Error())
	}
}
