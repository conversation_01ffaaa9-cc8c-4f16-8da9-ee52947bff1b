package fb

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	object_provider "github.com/protrip/uchiha-core/core/object-provider"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/spf13/viper"
	"io/ioutil"
	"net/http"
	"time"
)

type FBHandler struct {
	appID           string
	appSecret       string
	pageAccessToken string
	version         string
	baseUrl         string
}

func NewFBHandler() (*FBHandler, error) {
	appId := viper.GetString("facebook.app_id")
	appSecret := viper.GetString("facebook.app_secret")
	pageAccessToken := viper.GetString("facebook.page_access_token")
	if appId == "" || appSecret == "" || pageAccessToken == "" {
		return nil, fmt.Errorf("facebook credentials not found")
	}
	return &FBHandler{
		appID:           appId,
		appSecret:       appSecret,
		pageAccessToken: pageAccessToken,
		version:         "v22.0",
		baseUrl:         "https://graph.facebook.com/v22.0",
	}, nil
}

// MessageResponse represents the response from Facebook when sending a message
type MessageResponse struct {
	RecipientID string `json:"recipient_id"`
	MessageID   string `json:"message_id"`
	Error       *struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    int    `json:"code"`
	} `json:"error,omitempty"`
}

// TokenDebugResponse represents the response from the debug_token endpoint
type TokenDebugResponse struct {
	Data struct {
		AppID       string   `json:"app_id"`
		ExpiresAt   int64    `json:"expires_at"`
		IsValid     bool     `json:"is_valid"`
		Scopes      []string `json:"scopes"`
		Type        string   `json:"type"`
		UserID      string   `json:"user_id"`
		Application string   `json:"application"`
	} `json:"data"`
}

// TokenExchangeResponse represents the response from token exchange
type TokenExchangeResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresAt   int64  `json:"expires_at"`
}

// SendMessageToUser sends a text message to a specific user
func (f *FBHandler) SendMessageToUser(userID string, messageText string) (*MessageResponse, error) {
	url := f.baseUrl + "/me/messages"
	token, err := f.getToken()
	if err != nil {
		return nil, err
	}

	// Create the message payload
	requestBody, err := json.Marshal(map[string]interface{}{
		"recipient": map[string]string{
			"id": userID,
		},
		"message": map[string]string{
			"text": messageText,
		},
	})
	if err != nil {
		return nil, err
	}

	// Create the request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, err
	}

	// Add headers and query parameters
	req.Header.Set("Content-Type", "application/json")
	q := req.URL.Query()
	q.Add("access_token", token)
	req.URL.RawQuery = q.Encode()

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read and parse the response
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var messageResponse MessageResponse
	if err := json.Unmarshal(body, &messageResponse); err != nil {
		return nil, err
	}

	return &messageResponse, nil
}

// checkTokenValidity checks if a token is valid and returns its information
func (f *FBHandler) checkTokenValidity(accessToken string) (*TokenDebugResponse, error) {
	url := f.baseUrl + "/debug_token"

	// Create the request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	// Add query parameters
	q := req.URL.Query()
	q.Add("input_token", accessToken)
	q.Add("access_token", accessToken)
	req.URL.RawQuery = q.Encode()

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read and parse the response
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var tokenResponse TokenDebugResponse
	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return nil, err
	}

	return &tokenResponse, nil
}

// exchangeForLongLivedToken exchanges a short-lived token for a long-lived one
func (f *FBHandler) exchangeForLongLivedToken(shortLivedToken string) (*TokenExchangeResponse, error) {
	url := f.baseUrl + "/oauth/access_token"

	// Create the request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	// Add query parameters
	q := req.URL.Query()
	q.Add("grant_type", "fb_exchange_token")
	q.Add("client_id", f.appID)
	q.Add("client_secret", f.appSecret)
	q.Add("fb_exchange_token", shortLivedToken)
	req.URL.RawQuery = q.Encode()

	// Make the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read and parse the response
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var tokenExchangeResponse TokenExchangeResponse
	if err := json.Unmarshal(body, &tokenExchangeResponse); err != nil {
		return nil, err
	}

	return &tokenExchangeResponse, nil
}

// GetOrRefreshToken retrieves a token from Redis or exchanges for a new long-lived token
func (f *FBHandler) getToken() (string, error) {
	// Try to get token from Redis
	redisClient, err := object_provider.Supply[redis_client.RedisClient]()
	if err != nil {
		uchiha_log.Logger().Fatal("Can not supply redis client", "err", err.Error())
	}

	tokenString, err := redisClient.Get(context.Background(), "fb:token").Result()
	if err == nil && tokenString != "" {
		token := &TokenExchangeResponse{}
		json.Unmarshal([]byte(tokenString), token)
		if token.ExpiresAt > time.Now().Unix() {
			return token.AccessToken, nil
		}
	}

	// Token not found or error occurred, exchange for a new long-lived token
	tokenResponse, err := f.exchangeForLongLivedToken(f.pageAccessToken)
	if err != nil {
		return "", fmt.Errorf("failed to exchange for long-lived token: %w", err)
	}
	tokenResponse.ExpiresAt = time.Now().Add(time.Duration(24*30) * time.Hour).Unix()

	tokenJson, _ := json.Marshal(tokenResponse)
	// Save the new token to Redis with 30-day expiration
	err = redisClient.Set(context.Background(), "fb:token", string(tokenJson), 30*24*time.Hour).Err()
	if err != nil {
		return "", fmt.Errorf("failed to save token to Redis: %w", err)
	}

	return tokenResponse.AccessToken, nil
}

func main() {
	// Initialize the Facebook handler
	fbHandler := &FBHandler{
		appID:           "967260430983084",
		appSecret:       "6efc7557adace02d9a8fed75ba9e0d18",
		pageAccessToken: "EAANvt95k36wBOx6eI4bPB1Ef9LdCBxyeCzhrA1PUs6ETheSg2UrSUXfPuvuPFvmBr4ZBrwWwRRFZAj65zZALuYvc4oTSwXpH7PqU1aK3lzlSNuJzqOnPA85MwB7iMrJhsxFpbJjMFANGYzl3QsqhtH5B9iM4MQIr45rWsAprBnztmuZC3Yyaw0wjVCpFTUlJcexejUqS5QBDfYSQEAZDZD",
		version:         "v22.0",
		baseUrl:         "https://graph.facebook.com/v22.0",
	}

	// Example 1: Check if your token is valid and when it expires
	tokenInfo, err := fbHandler.checkTokenValidity(fbHandler.pageAccessToken)
	if err != nil {
		fmt.Printf("Error checking token: %v\n", err)
	} else {
		if tokenInfo.Data.IsValid {
			expirationTime := time.Unix(tokenInfo.Data.ExpiresAt, 0)
			fmt.Printf("Token is valid! Expires at: %v\n", expirationTime)
			fmt.Printf("Token scopes: %v\n", tokenInfo.Data.Scopes)
		} else {
			fmt.Println("Token is invalid!")
		}
	}

	// Example 2: Exchange for a long-lived token
	tokenResponse, err := fbHandler.exchangeForLongLivedToken(fbHandler.pageAccessToken)
	if err != nil {
		fmt.Printf("Error exchanging token: %v\n", err)
	} else {
		fmt.Printf("New long-lived token: %s\n", tokenResponse.AccessToken)
		fmt.Printf("Token expires in %d seconds\n", tokenResponse.ExpiresAt)
	}

	// Example 3: Send a message to a user
	userID := "9711474738894799" // Replace with actual user ID
	messageResponse, err := fbHandler.SendMessageToUser(userID, "Hello from Go!")
	if err != nil {
		fmt.Printf("Error sending message: %v\n", err)
	} else {
		if messageResponse.Error != nil {
			fmt.Printf("Error in response: %s (Code: %d)\n", messageResponse.Error.Message, messageResponse.Error.Code)
		} else {
			fmt.Printf("Message sent successfully! Message ID: %s\n", messageResponse.MessageID)
		}
	}
}
