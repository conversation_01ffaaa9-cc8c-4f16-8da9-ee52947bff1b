package notify

import (
	"barber-api/pkg/facebook"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/spf13/viper"
)

type FacebookNotifier struct {
	Handler *fb.FBHandler
}

func NewFacebookNotifier(handler *fb.FBHandler) *FacebookNotifier {
	return &FacebookNotifier{Handler: handler}
}

func (f *FacebookNotifier) Send(message string) error {
	userId := viper.GetString("facebook.user_id")
	if userId == "" {
		uchiha_log.Logger().Warn("user_id is not found")
		return nil
	}
	_, err := f.Handler.SendMessageToUser(userId, message)
	return err
}
