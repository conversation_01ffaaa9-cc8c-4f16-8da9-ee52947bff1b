package request_parser

import (
	uchiha_models "github.com/protrip/uchiha-core/exmsg/models"
	"github.com/spf13/cast"
	"net/http"
)

func OrmParamsParser(r *http.Request) (pageParams *uchiha_models.CommonRequest) {
	pageParams = &uchiha_models.CommonRequest{}
	urlQuery := r.URL.Query()

	pageParams.Limit = cast.ToInt64(urlQuery.Get("limit"))
	pageParams.Page = cast.ToInt64(urlQuery.Get("page"))

	return pageParams
}
