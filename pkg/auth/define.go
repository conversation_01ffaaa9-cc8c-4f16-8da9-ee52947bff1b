package auth

import "context"

type UserRequest struct {
	ID       int64  `json:"id"`
	Username string `json:"username"`
	Type     string `json:"type"`

	ConfirmationToken string `json:"confirmation_token"`
	PasswordResetAt   int    `json:"password_reset_at"`

	UpdateFields []string `json:"update_fields"`
	User         *User    `json:"user"`
}

type User struct {
	ID                  int64  `json:"id"`
	Username            string `json:"username"`
	Email               string `json:"email"`
	Password            string `json:"password"`
	Salt                string `json:"salt"`
	Avatar              string `json:"avatar"`
	FirstName           string `json:"first_name"`
	LastName            string `json:"last_name"`
	ConfirmationToken   string `json:"confirmation_token"`
	PasswordResetAt     int64  `json:"password_reset_at"`
	PasswordRequestedAt int64  `json:"password_requested_at"`
}

const (
	UserTypeEmployee  = "em"
	UserTypeAdminUser = "au"
)

var (
	AcceptedAuthTypes = map[string]bool{
		UserTypeEmployee:  true,
		UserTypeAdminUser: true,
	}
)

type UserInterface interface {
	//HttpPath() string
	GetUser(ctx context.Context, req *UserRequest) (u *User, err error)
	UpdateUser(ctx context.Context, req *UserRequest) (u *User, err error)
	//GetEmailRequest(ctx context.Context, event string, user *User, extraParams map[string]interface{}) (u *mailer.SendEmailMessage, err error)
}

type Credentials struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type CredentialsVerifyResetPassword struct {
	ResetPasswordToken string `json:"reset_password_token"`
	OldPassword        string `json:"old_password"`
	NewPassword        string `json:"new_password"`
	Logout             bool   `json:"logout"`
	X                  bool   `json:"x"`
}
