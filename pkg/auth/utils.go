package auth

import (
	"context"
	redis_client "github.com/protrip/uchiha-core/core/drivers/redis-client"
	uchiha_log "github.com/protrip/uchiha-core/core/uchiha-log"
	"github.com/spf13/cast"
	"math/rand"
	"strings"
	"time"
)

func ClearAccessTokenByTokenTypeAndUserType(ctx context.Context, rd *redis_client.RedisClient, tokenType, userType string, userID int64) (err error) {
	var cursor uint64
	prefixes := make([]string, 0)
	tokenPrefix := GetTokenPrefix(tokenType)
	if len(tokenPrefix) > 0 {
		prefixes = append(prefixes, tokenPrefix)
	}

	prefixes = append(prefixes, userType, cast.ToString(userID))

	for {
		var keys []string
		scanCmd := rd.Scan(ctx, cursor, strings.Join(prefixes, ":"), 1000)
		keys, cursor, err = scanCmd.Result()
		if err != nil {
			uchiha_log.Logger().Error("ERROR on scan key redis:", err)
			return err
		}

		rd.Del(ctx, keys...).Result()

		if cursor == 0 {
			break
		}

		time.Sleep(time.Millisecond * 50)
	}

	return nil
}

func GetTokenPrefix(tokenType string) string {
	for _, def := range TokenDefinitions {
		if def.TokenType == tokenType {
			return def.TokenPrefix
		}
	}

	return ""
}

var (
	lowerCharSet   = "abcdedfghijklmnopqrst"
	upperCharSet   = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	specialCharSet = "!@#$%&*"
	numberSet      = "0123456789"
	allCharSet     = lowerCharSet + upperCharSet + specialCharSet + numberSet
)

func GeneratePassword(passwordLength, minSpecialChar, minNum, minUpperCase int) string {
	var password strings.Builder

	//Set special character
	for i := 0; i < minSpecialChar; i++ {
		random := rand.Intn(len(specialCharSet))
		password.WriteString(string(specialCharSet[random]))
	}

	//Set numeric
	for i := 0; i < minNum; i++ {
		random := rand.Intn(len(numberSet))
		password.WriteString(string(numberSet[random]))
	}

	//Set uppercase
	for i := 0; i < minUpperCase; i++ {
		random := rand.Intn(len(upperCharSet))
		password.WriteString(string(upperCharSet[random]))
	}

	remainingLength := passwordLength - minSpecialChar - minNum - minUpperCase
	for i := 0; i < remainingLength; i++ {
		random := rand.Intn(len(allCharSet))
		password.WriteString(string(allCharSet[random]))
	}
	inRune := []rune(password.String())
	rand.Shuffle(len(inRune), func(i, j int) {
		inRune[i], inRune[j] = inRune[j], inRune[i]
	})
	return string(inRune)
}

//func IsStaffValid(r *http.Request) bool {
//	return cast.ToBool(r.Header.Get(XStaffValid))
//}
