package auth

import (
	"barber-api/cmd/dms/server"
	"barber-api/exmsg/models"
	"barber-api/exmsg/services"
	"context"
)

type AdminUser struct {
	AdminDmsClient *server.AdminDmsServer
}

func (e AdminUser) GetUser(ctx context.Context, req *UserRequest) (u *User, err error) {
	// get user
	user, err := e.AdminDmsClient.GetAdminUserByOptions(ctx, &services.AdminUserRequest{
		Id:                req.ID,
		Email:             req.Username,
		ConfirmationToken: req.ConfirmationToken,
	})
	if err != nil {
		return
	}

	return e.toUser(user), nil
}

func (e AdminUser) UpdateUser(ctx context.Context, req *UserRequest) (u *User, err error) {
	if req.User == nil {
		return
	}

	// Prevent update field to NULL
	if len(req.UpdateFields) == 0 {
		return
	}

	user := e.toAdminUser(req.User)

	// update by id
	_, err = e.AdminDmsClient.UpdateAdminUser(ctx, &services.AdminUserRequest{
		Id:            req.ID,
		Model:         user,
		UpdatedFields: req.UpdateFields,
	})

	if err != nil {
		return
	}

	return req.User, nil
}

func (e AdminUser) toUser(user *models.AdminUser) *User {
	return &User{
		ID:                user.Id,
		Username:          user.Email,
		Email:             user.Email,
		Password:          user.Password,
		Salt:              user.Salt,
		FirstName:         user.FirstName,
		LastName:          user.LastName,
		ConfirmationToken: user.ConfirmationToken,
		PasswordResetAt:   user.PasswordResetAt,
	}
}

func (e AdminUser) toAdminUser(user *User) *models.AdminUser {
	return &models.AdminUser{
		Id:                user.ID,
		Email:             user.Email,
		Password:          user.Password,
		FirstName:         user.FirstName,
		LastName:          user.LastName,
		ConfirmationToken: user.ConfirmationToken,
		PasswordResetAt:   user.PasswordResetAt,
		Salt:              user.Salt,
	}
}

//func (e AdminUser) GetEmailRequest(ctx context.Context, eventType string, user *auth.User, extraParams map[string]interface{}) (u *mailer.SendEmailMessage, err error) {
//	info := mailer.GetOrganizationEmailInfo()
//	switch eventType {
//	case auth.EventTypeResetPassword:
//		return &mailer.SendEmailMessage{
//			EmailData: mailer.EmailData{
//				Subject: "Reset password on Thetraineeclub.com admin",
//				From: mailer.EmailAddress{
//					Name:  info.Name,
//					Email: info.Email,
//				},
//				To: mailer.EmailAddress{
//					Name:  fmt.Sprintf("%v %v", user.FirstName, user.LastName),
//					Email: user.Email,
//				},
//				ReplyTo: info.ReplyTo,
//				Payload: extraParams,
//			},
//			Event: email_template.EventAdminUserEmailResetPassword,
//		}, nil
//
//	default:
//		return nil, nil
//	}
//}
//
