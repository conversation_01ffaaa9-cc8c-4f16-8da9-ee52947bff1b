package auth

//import (
//	"context"
//	"fmt"
//	mt_services "protrip-api/exmsg/services"
//	"protrip-api/pkg/auth"
//	"protrip-api/pkg/codename"
//	"protrip-api/pkg/email_template"
//	"protrip-api/pkg/mailer"
//)
//
//type Employee struct {
//	EmployeeClient mt_services.EmployeeClient
//}
//
//func (e Employee) HttpPath() string {
//	return codename.APIEmployee.HTTPBasePath
//}
//
//func (e Employee) GetEmailRequest(ctx context.Context, eventType string, user *auth.User, extraParams map[string]interface{}) (u *mailer.SendEmailMessage, err error) {
//	info := mailer.GetOrganizationEmailInfo()
//	switch eventType {
//	case auth.EventTypeResetPassword:
//		return &mailer.SendEmailMessage{
//			EmailData: mailer.EmailData{
//				Subject: "Reset password at Thetraineeclub.com",
//				From: mailer.EmailAddress{
//					Name:  info.Name,
//					Email: info.Email,
//				},
//				To: mailer.EmailAddress{
//					Name:  fmt.Sprintf("%v %v", user.FirstName, user.LastName),
//					Email: user.Email,
//				},
//				ReplyTo: info.ReplyTo,
//				Payload: extraParams,
//			},
//			Event: email_template.EventEmployeeEmailResetPassword,
//		}, nil
//
//	default:
//		return nil, nil
//	}
//}
//
//func (e Employee) GetUser(ctx context.Context, req *auth.UserRequest) (u *auth.User, err error) {
//	// get user
//	user, err := e.EmployeeClient.GetEmployeeByOptions(ctx, &mt_services.EmployeeRequest{
//		Id:                req.ID,
//		Email:             req.Username,
//		ConfirmationToken: req.ConfirmationToken,
//	})
//	if err != nil {
//		return
//	}
//
//	return e.toUser(user), nil
//}
//
//func (e Employee) UpdateUser(ctx context.Context, req *auth.UserRequest) (u *auth.User, err error) {
//	if req.User == nil {
//		return
//	}
//
//	// Prevent update field to NULL
//	if len(req.UpdateFields) == 0 {
//		return
//	}
//
//	employee := e.toEmployee(req.User)
//
//	// update by id
//	_, err = e.EmployeeClient.UpdateEmployee(ctx, &mt_services.EmployeeRequest{
//		Id:            req.ID,
//		Employee:      employee,
//		UpdatedFields: req.UpdateFields,
//	})
//
//	if err != nil {
//		return
//	}
//
//	return req.User, nil
//}
//
//func (e Employee) toUser(user *mt_models.Employee) *auth.User {
//	return &auth.User{
//		ID:                  user.Id,
//		Username:            user.Email,
//		Email:               user.Email,
//		Password:            user.Password,
//		Salt:                user.Salt,
//		FirstName:           user.FirstName,
//		LastName:            user.LastName,
//		ConfirmationToken:   user.ConfirmationToken,
//		PasswordResetAt:     user.PasswordResetAt,
//		PasswordRequestedAt: user.PasswordRequestedAt,
//	}
//}
//
//func (e Employee) toEmployee(user *auth.User) *mt_models.Employee {
//	return &mt_models.Employee{
//		Id:                  user.ID,
//		Email:               user.Email,
//		Password:            user.Password,
//		FirstName:           user.FirstName,
//		LastName:            user.LastName,
//		ConfirmationToken:   user.ConfirmationToken,
//		PasswordResetAt:     user.PasswordResetAt,
//		PasswordRequestedAt: user.PasswordRequestedAt,
//		Salt:                user.Salt,
//	}
//}
