package auth

import (
	"barber-api/cmd/dms/server"
	"context"
	"fmt"
	"go-micro.dev/v5/errors"
)

type UserHelper struct {
	AdminDms *server.AdminDmsServer
}

func (u *UserHelper) GetUser(ctx context.Context, req *UserRequest) (user *User, err error) {
	if len(req.Type) == 0 {
		return nil, errors.New("missing_user_type", "missing user type", 400)
	}

	if !AcceptedAuthTypes[req.Type] {
		return nil, errors.New("type_not_supported", fmt.Sprintf("user type %v is not supported", req.Type), 400)
	}

	processor, err := u.GetUserProcessor(req.Type)
	if err != nil {
		return
	}

	return processor.GetUser(ctx, req)
}

func (u *UserHelper) GetUserProcessor(userType string) (UserInterface, error) {
	switch userType {
	//case auth.UserTypeEmployee:
	//	return &Employee{
	//		EmployeeClient: u.EmployeeClient,
	//	}, nil

	case UserTypeAdminUser:
		return &AdminUser{AdminDmsClient: u.AdminDms}, nil

	default:
		return nil, errors.New("type_not_supported", fmt.Sprintf("user type %v is not supported", userType), 400)
	}
}
