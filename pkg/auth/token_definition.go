package auth

import (
	"github.com/protrip/uchiha-core/core/tokenmanager"
)

const (
	TokenTypeUserAccessToken      = "uat"
	TokenTypeResetPassword        = "uatpw"
	TokenTypeAdminUserAccessToken = "auat"
	TokenTypeEmployeeAccessToken  = "euat"
)

// TODO if this var is modified, u must rebuild api-gateway
var TokenDefinitions = []*tokenmanager.TokenDefinition{
	&tokenmanager.TokenDefinition{
		TokenType:    TokenTypeUserAccessToken,
		TokenPrefix:  "uat",
		TTL:          30 * 24 * 60 * 60,
		SaveToCookie: true,
	},

	&tokenmanager.TokenDefinition{
		TokenType:   TokenTypeResetPassword,
		TokenPrefix: "uatpw",
		TTL:         30 * 60,
	},

	&tokenmanager.TokenDefinition{
		TokenType:    TokenTypeAdminUserAccessToken,
		TokenPrefix:  TokenTypeAdminUserAccessToken,
		TTL:          30 * 24 * 60 * 60,
		SaveToCookie: true,
	},

	&tokenmanager.TokenDefinition{
		TokenType:    TokenTypeEmployeeAccessToken,
		TokenPrefix:  TokenTypeEmployeeAccessToken,
		TTL:          30 * 24 * 60 * 60,
		SaveToCookie: true,
	},
}

//
//func GetTokenDefinitionsFromConfig() []*tokenmanager.TokenDefinition {
//	def := make([]*tokenmanager.TokenDefinition, 0)
//	defURI := viper.GetString("token_definition.uri")
//	if len(defURI) == 0 {
//		return TokenDefinitions
//	}
//	taskDefinition, err := config.ReadRawFile(defURI)
//	if err != nil {
//		logger.BkLog.Errorw("Error when read")
//	}
//	taskDefinition, err = config.ReadRawFile("")
//}
