package telegram

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/viper"
	"net/http"
)

type TelegramHandler struct {
	BotToken string
	ChatID   int64
}

func NewTelegramHandler() (*TelegramHandler, error) {
	botToken := viper.GetString("telegram.bot_token")
	chatID := viper.GetInt64("telegram.chat_id")
	if botToken == "" || chatID == 0 {
		return nil, errors.New("telegram credentials not found")
	}
	return &TelegramHandler{
		BotToken: botToken,
		ChatID:   chatID,
	}, nil
}

func (t *TelegramHandler) SendMessage(message string) error {
	url := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", t.BotToken)

	payload := map[string]interface{}{
		"chat_id": t.ChatID,
		"text":    message,
	}

	body, _ := json.Marshal(payload)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send telegram message, status: %v", resp.Status)
	}

	return nil
}
