# This is the base image of the services.
# In this stage we prepare all required common data to dockerize our services.
FROM golang:1.24

ARG BIN
ENV CMD_DIR ${BIN}

WORKDIR /barber-api
COPY . .

ENV GO111MODULE=on

RUN make native BIN=$CMD_DIR

RUN mkdir /app && cp -r dist/$CMD_DIR/* /app
#COPY conf_docker.toml /app/conf.toml

RUN mkdir -p /app/jwt

COPY jwt /app/jwt

COPY conf_docker.toml /app/conf.toml

# expose port for api
EXPOSE 8888
# expose port for dms
EXPOSE 8899

WORKDIR /app

ENTRYPOINT ["./run.sh", "start"]