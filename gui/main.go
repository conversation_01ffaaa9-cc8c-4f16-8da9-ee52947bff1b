package main

import (
	"fmt"
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"
	"fyne.io/fyne/v2/widget"
)

func main() {
	myApp := app.New()
	myWindow := myApp.NewWindow("List Data")

	data := binding.BindStringList(
		&[]string{"Item 1", "Item 2", "Item 3"},
	)

	list := widget.NewListWithData(data,
		func() fyne.CanvasObject {
			return widget.NewLabel("HELLLO")
		},
		func(i binding.DataItem, o fyne.CanvasObject) {
			o.(*widget.Label).Bind(i.(binding.String))
		})
	list.Resize(fyne.NewSize(400, 300))

	add := widget.NewButton("Append", func() {
		val := fmt.Sprintf("Hello Bạn vừa nhận được %d", data.Length()+1)
		data.Append(val)
	})

	add.Resize(fyne.NewSize(400, 100))
	myWindow.SetContent(container.NewBorder(add, nil, nil, nil, container.NewVScroll(list)))
	myWindow.Resize(fyne.NewSize(400, 400))
	myWindow.ShowAndRun()
}
