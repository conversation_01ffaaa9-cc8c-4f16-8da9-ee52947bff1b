services:
  barber-admin-api:
    platform: linux/amd64
    image: "punkyy/barber-admin-api:v1.0.0"
    build:
      context: .
      dockerfile: Dockerfile
      args:
        BIN: "cmd/api/admin-api"
    volumes:
      - ./conf_docker.toml:/app/conf.toml
      - ./jwt:/app/jwt
    depends_on:
      - postgres
      - redis

    entrypoint:
      [
        "./run.sh",
        "start",
        "-config-type=file",
        "-config-file=/app/conf.toml",
        "-http-port=30099",
      ]
    network_mode: "host"
    restart: always

  barber-landing-api:
      platform: linux/amd64
      image: "punkyy/barber-landing-api:v1.0.0"
      build:
        context: .
        dockerfile: Dockerfile
        args:
          BIN: "cmd/api/landing-api"
      volumes:
        - ./conf_docker.toml:/app/conf.toml
      entrypoint:
        [
          "./run.sh",
          "start",
          "-config-type=file",
          "-config-file=/app/conf.toml",
          "-http-port=30077",
        ]
      network_mode: "host"
      restart: always

  postgres:
    image: postgres:14-alpine
    container_name: barber-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: barber
      POSTGRES_PASSWORD: security
      POSTGRES_DB: barber
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: always

  redis:
    image: redis:7-alpine
    container_name: barber-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: always

volumes:
  postgres_data:
  redis_data:
