[postgre]
host = "localhost"
port = 5432
protocol = "tcp"
user = "barber"
password = "security"
database_name = "barber"

[jwt]
private_key_path = "jwt/private.key"
public_key_path = "jwt/public.key"
encrypt_key_path = "jwt/encrypt.key"

[redis]
address = "localhost:6379"

[local]
#debug=1

[facebook]
app_id = "967260430983084"
app_secret = "6efc7557adace02d9a8fed75ba9e0d18"
page_access_token = "EAANvt95k36wBOx6eI4bPB1Ef9LdCBxyeCzhrA1PUs6ETheSg2UrSUXfPuvuPFvmBr4ZBrwWwRRFZAj65zZALuYvc4oTSwXpH7PqU1aK3lzlSNuJzqOnPA85MwB7iMrJhsxFpbJjMFANGYzl3QsqhtH5B9iM4MQIr45rWsAprBnztmuZC3Yyaw0wjVCpFTUlJcexejUqS5QBDfYSQEAZDZD"
#minh
user_id = "9711474738894799"
#user_id = "9625663304166756"


[telegram]
bot_token = "**********************************************"
chat_id = -4625808254