package dto

import (
	"barber-api/exmsg/models"
	"time"
)

type BookingDto struct {
	Id         int64            `json:"id,omitempty"`
	Customer   *models.Customer `json:"customer,omitempty"`
	StaffId    int64            `json:"staff_id,omitempty"`
	Date       string           `json:"date,omitempty"`
	TimePeriod string           `json:"time_period,omitempty"`
	Note       string           `json:"note,omitempty"`
	CreatedAt  int64            `json:"created_at,omitempty"`
	UpdatedAt  int64            `json:"updated_at,omitempty"`
	Services   []*ServiceDto    `json:"services,omitempty"`
	Stylist    *models.Staff    `json:"stylist,omitempty"`
	Status     string           `json:"status,omitempty"`
}

func ConvertToBookingDto(proto *models.Booking) *BookingDto {
	if proto == nil {
		return nil
	}

	// Format date from ISO format to YYYY-MM-DD
	formattedDate := proto.Date
	if t, err := time.Parse(time.RFC3339, proto.Date); err == nil {
		formattedDate = t.Format("2006-01-02")
	}

	return &BookingDto{
		Id:         proto.Id,
		StaffId:    proto.StaffId,
		Customer:   &models.Customer{Id: proto.CustomerId},
		Date:       formattedDate,
		TimePeriod: proto.TimePeriodName,
		Note:       proto.Note,
		CreatedAt:  proto.CreatedAt,
		UpdatedAt:  proto.UpdatedAt,
		Services:   make([]*ServiceDto, 0),
		Status:     proto.Status,
	}
}
