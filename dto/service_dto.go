package dto

import "barber-api/exmsg/models"

type AdminServiceDto struct {
	*ServiceDto
	ServiceCustoms []*models.ServiceCustom `json:"service_customs"`
}

type ServiceDto struct {
	Id          int64   `json:"id,omitempty"`
	Name        string  `json:"name,omitempty"`
	Price       float64 `json:"price,omitempty"`
	Description string  `json:"description,omitempty"`
	CreatedAt   int64   `json:"created_at,omitempty"`
	UpdatedAt   int64   `json:"updated_at,omitempty"`
}

type ServiceCustomDto struct {
	Id        int64   `json:"id"`
	ServiceId int64   `json:"service_id"`
	StylistId int64   `json:"stylist_id"`
	TimeSlot  string  `json:"time_slot"`
	Price     float64 `json:"price"`
}

// ConvertServiceDto convert models to dto
func ConvertToServiceDto(proto *models.Service) *ServiceDto {
	if proto == nil {
		return nil
	}
	return &ServiceDto{
		Id:          proto.Id,
		Name:        proto.Name,
		Price:       proto.Price,
		Description: proto.Description,
		CreatedAt:   proto.CreatedAt,
		UpdatedAt:   proto.UpdatedAt,
	}
}

func ConvertListServiceToServiceDto(proto []*models.Service) []*ServiceDto {
	if proto == nil {
		return nil
	}

	res := make([]*ServiceDto, 0)
	for _, service := range proto {
		res = append(res, ConvertToServiceDto(service))
	}

	return res
}

func ConvertListServiceCustomToServiceCustomDto(proto []*models.ServiceCustom) []*ServiceCustomDto {
	if proto == nil {
		return nil
	}

	res := make([]*ServiceCustomDto, 0)
	for _, serviceCustom := range proto {
		res = append(res, &ServiceCustomDto{
			Id:        serviceCustom.Id,
			ServiceId: serviceCustom.ServiceId,
			StylistId: serviceCustom.StaffId,
			TimeSlot:  serviceCustom.TimePeriodName,
			Price:     serviceCustom.Price,
		})
	}

	return res
}
