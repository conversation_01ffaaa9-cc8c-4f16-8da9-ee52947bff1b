package dto

import "barber-api/exmsg/models"

// Deprecated

type AdminStaffDto struct {
	*StaffDto
	IsAvailable bool               `json:"is_available,omitempty"`
	StaffOffs   []*models.StaffOff `json:"staff_offs,omitempty"`
}

type StaffDto struct {
	Id   int64  `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type StaffOffDto struct {
	Id        int64  `json:"id,omitempty"`
	StylistId int64  `json:"stylist_id,omitempty"`
	Date      string `json:"date,omitempty"`
	TimeSlot  string `json:"time_slot,omitempty"`
}

func ConvertListStaffToStaffDto(proto []*models.Staff) []*StaffDto {
	if proto == nil {
		return nil
	}

	res := make([]*StaffDto, 0)
	for _, staff := range proto {
		res = append(res, &StaffDto{
			Id:   staff.Id,
			Name: staff.Name,
		})
	}

	return res
}

func ConvertListStaffOffToStaffOffDto(proto []*models.StaffOff) []*StaffOffDto {
	if proto == nil {
		return nil
	}

	res := make([]*StaffOffDto, 0)
	for _, staffOff := range proto {
		res = append(res, &StaffOffDto{
			Id:        staffOff.Id,
			StylistId: staffOff.StaffId,
			Date:      staffOff.Date,
			TimeSlot:  staffOff.TimePeriodName,
		})
	}

	return res
}
