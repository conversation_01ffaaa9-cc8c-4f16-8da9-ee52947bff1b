package dto

import "barber-api/exmsg/models"

type AdminUserDto struct {
	Id        int64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Email     string `protobuf:"bytes,2,opt,name=email" json:"email,omitempty"`
	FirstName string `protobuf:"bytes,4,opt,name=first_name,json=firstName" json:"first_name,omitempty"`
	LastName  string `protobuf:"bytes,5,opt,name=last_name,json=lastName" json:"last_name,omitempty"`
	CreatedAt int64  `protobuf:"varint,12,opt,name=created_at,json=createdAt" json:"created_at,omitempty"`
	UpdatedAt int64  `protobuf:"varint,13,opt,name=updated_at,json=updatedAt" json:"updated_at,omitempty"`
}

func ConvertAdminUserDto(proto *models.AdminUser) *AdminUserDto {
	return &AdminUserDto{
		Id:        proto.Id,
		Email:     proto.Email,
		FirstName: proto.FirstName,
		LastName:  proto.LastName,
	}
}
