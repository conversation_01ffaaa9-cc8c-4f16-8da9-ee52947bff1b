#version: "3.7"
services:
  pg:
    container_name: barber-api-pg-container
    image: postgres:14-alpine
    ports:
      - 5432:5432
    networks:
      - network
    environment:
      - POSTGRES_USER=barber
      - POSTGRES_PASSWORD=security
      - POSTGRES_DB=barber

  pg-migrate:
    container_name: barber-api-pg-migrate
    image: migrate/migrate:v4.15.2
    volumes:
      - ./migrations:/migrations
    restart: "no"
    networks:
      - network
    environment:
      TZ: UTC
      POSTGRES_URL: postgres://{POSTGRES_USER}:{POSTGRES_PASSWORD}@pg:5432/{POSTGRES_DB}?sslmode=disable

networks:
  network:
    name: network
