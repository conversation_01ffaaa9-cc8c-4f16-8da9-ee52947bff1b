--- customer table ---
CREATE TABLE IF NOT EXISTS customers
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name         VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    phone_number VARCHAR(25)  NOT NULL UNIQUE,
    created_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--- staff table ---
CREATE TABLE IF NOT EXISTS staff
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name         VARCHAR(255) NOT NULL,
    is_available BOOLEAN      NOT NULL    DEFAULT FALSE,
    created_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--- time_period table ---
CREATE TABLE IF NOT EXISTS time_periods
(
    id         BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name       <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE, -- The field represents time intervals in 30-minute increments. Ex: 9h00, 9h30,...
    created_at TIMESTA<PERSON> WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--- staff_off table ---
CREATE TABLE IF NOT EXISTS staff_off
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    staff_id         BIGINT NOT NULL REFERENCES staff (id) ON DELETE CASCADE,
    date             DATE   NOT NULL,
    time_period_name VARCHAR(50),
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_staff_date UNIQUE (staff_id, date, time_period_name)
);

CREATE INDEX idx_staff_off_staff_id_date ON staff_off (staff_id, date);

--- service table ---
CREATE TABLE IF NOT EXISTS services
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name         VARCHAR(255) NOT NULL,
    price        NUMERIC,
    description  TEXT,
    is_available BOOLEAN      NOT NULL    DEFAULT FALSE,
    created_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--- service_custom table ---
CREATE TABLE IF NOT EXISTS services_custom
(
    id               BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    service_id       BIGINT      NOT NULL REFERENCES services (id) ON DELETE CASCADE,
    staff_id         BIGINT      NOT NULL     DEFAULT 0,
    time_period_name VARCHAR(50) NOT NULL,
    price            NUMERIC,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_services_custom UNIQUE (service_id, staff_id, time_period_name)
);

--- booking table ---
create table bookings
(
    id               bigint generated by default as identity
        primary key,
    customer_id      bigint      not null
        references customers (id) on delete set null,
    staff_id         bigint      not null     default 0,
    date             date        not null,
    time_period_name varchar(50) not null,
    note             text,
    booking_services jsonb                    default '[]'::jsonb not null,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now(),
    status           varchar(20)
);

--- transaction table ---
CREATE TABLE IF NOT EXISTS transactions
(
    id         BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    staff_id   BIGINT NOT NULL          DEFAULT 0,
    booking_id BIGINT NOT NULL          DEFAULT 0,
    price      NUMERIC,
    note       TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--- address table ---
CREATE TABLE IF NOT EXISTS addresses
(
    id           BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    name         VARCHAR(255),
    address      VARCHAR(255),
    phone_number VARCHAR(25),
    created_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at   TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

--- admin_user table ---
CREATE TABLE IF NOT EXISTS admin_user
(
    id                 BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    email              VARCHAR(255) NOT NULL UNIQUE,
    password           VARCHAR(255) NOT NULL,
    first_name         VARCHAR(255),
    last_name          VARCHAR(255),
    salt               VARCHAR(255), -- The field is a key that used to hash password
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at         TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmation_token VARCHAR(255),
    password_reset_at  TIMESTAMP WITH TIME ZONE
);

WITH RECURSIVE time_series AS (
    -- Base case: start with 00:00
    SELECT '00:00'::time AS time
    UNION ALL
    -- Recursive case: add 30 minutes until we reach 23:30
    SELECT (time + interval '30 minutes')::time
    FROM time_series
    WHERE time < '23:30')
INSERT
INTO time_periods (name)
SELECT to_char(time, 'HH24:MI')
FROM time_series;